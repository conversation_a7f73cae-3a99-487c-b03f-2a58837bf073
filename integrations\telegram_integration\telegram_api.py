"""
Telegram Messaging Integration via Unipile API and Direct Bot API
Handles authentication and messaging for Telegram using both Unipile and direct Bot API

Enhanced version with:
- Improved error handling and retry logic
- Async messaging support
- Better rate limiting
- Rich message formatting
- Enhanced connection management
- Message validation and sanitization
- Comprehensive logging
"""

import requests
import json
import time
import os
import re
import html
from typing import Dict, List, Optional, Union, Tuple, Any
import logging
from datetime import datetime, timedelta
import asyncio
import aiohttp
from dataclasses import dataclass
from enum import Enum

class MessageType(Enum):
    """Telegram message types"""
    TEXT = "text"
    PHOTO = "photo"
    DOCUMENT = "document"
    VIDEO = "video"
    AUDIO = "audio"
    VOICE = "voice"
    STICKER = "sticker"
    LOCATION = "location"
    CONTACT = "contact"

class ConnectionStatus(Enum):
    """Connection status states"""
    DISCONNECTED = "disconnected"
    CONNECTING = "connecting"
    CONNECTED = "connected"
    ERROR = "error"
    NOT_CONFIGURED = "not_configured"

@dataclass
class MessageResult:
    """Result of a message sending operation"""
    success: bool
    message_id: Optional[str] = None
    error: Optional[str] = None
    platform: str = "telegram"
    method: Optional[str] = None  # 'unipile' or 'bot_api'
    timestamp: Optional[datetime] = None
    retry_count: int = 0

@dataclass
class RateLimitConfig:
    """Rate limiting configuration"""
    messages_per_second: float = 30.0
    messages_per_minute: int = 20
    messages_per_chat_per_second: float = 1.0
    burst_limit: int = 5

def get_config_path() -> str:
    """Get the absolute path to the config file"""
    # Get the directory where this script is located
    current_dir = os.path.dirname(os.path.abspath(__file__))
    return os.path.join(current_dir, "config.json")

def sanitize_message(message: str, max_length: int = 4096) -> str:
    """Sanitize and validate message content"""
    if not message or not isinstance(message, str):
        return ""

    # Remove potentially harmful characters
    message = re.sub(r'[\x00-\x08\x0B\x0C\x0E-\x1F\x7F]', '', message)

    # Truncate if too long
    if len(message) > max_length:
        message = message[:max_length-3] + "..."

    return message.strip()

def format_html_message(text: str, bold: bool = False, italic: bool = False,
                       code: bool = False, pre: bool = False) -> str:
    """Format message with HTML tags for Telegram"""
    if not text:
        return ""

    # Escape HTML entities first
    text = html.escape(text)

    if bold:
        text = f"<b>{text}</b>"
    if italic:
        text = f"<i>{text}</i>"
    if code:
        text = f"<code>{text}</code>"
    if pre:
        text = f"<pre>{text}</pre>"

    return text

class UnipileClient:
    """Enhanced Unipile API client for Telegram integration with retry logic and better error handling"""

    def __init__(self, api_key: str, max_retries: int = 3, retry_delay: float = 1.0):
        self.api_key = api_key
        self.base_url = "https://api8.unipile.com:13814/api/v1"
        self.max_retries = max_retries
        self.retry_delay = retry_delay
        self.headers = {
            "X-API-KEY": self.api_key,
            "accept": "application/json",
            "Content-Type": "application/json"
        }

        # Setup logging
        logging.basicConfig(level=logging.INFO)
        self.logger = logging.getLogger(__name__)

        # Rate limiting
        self.last_request_time = 0
        self.min_request_interval = 0.5  # 2 requests per second

    def _rate_limit(self):
        """Implement rate limiting for Unipile API"""
        current_time = time.time()
        time_since_last_request = current_time - self.last_request_time

        if time_since_last_request < self.min_request_interval:
            sleep_time = self.min_request_interval - time_since_last_request
            time.sleep(sleep_time)

        self.last_request_time = time.time()

    def make_request(self, method: str, endpoint: str, data: Dict = None) -> Dict:
        """Make HTTP request to Unipile API with retry logic"""
        url = f"{self.base_url}/{endpoint.lstrip('/')}"

        for attempt in range(self.max_retries + 1):
            try:
                self._rate_limit()

                if method.upper() == "GET":
                    response = requests.get(url, headers=self.headers, timeout=30)
                elif method.upper() == "POST":
                    response = requests.post(url, headers=self.headers, json=data, timeout=30)
                else:
                    raise ValueError(f"Unsupported HTTP method: {method}")

                response.raise_for_status()
                result = response.json() if response.content else {"success": True}

                # Log successful request
                if attempt > 0:
                    self.logger.info(f"Request succeeded on attempt {attempt + 1}")

                return result

            except requests.exceptions.Timeout as e:
                self.logger.warning(f"Request timeout on attempt {attempt + 1}: {e}")
                if attempt < self.max_retries:
                    time.sleep(self.retry_delay * (2 ** attempt))  # Exponential backoff
                    continue
                return {"error": f"Request timeout after {self.max_retries + 1} attempts"}

            except requests.exceptions.RequestException as e:
                self.logger.warning(f"Request failed on attempt {attempt + 1}: {e}")
                if attempt < self.max_retries:
                    time.sleep(self.retry_delay * (2 ** attempt))  # Exponential backoff
                    continue
                return {"error": f"Request failed after {self.max_retries + 1} attempts: {str(e)}"}

            except Exception as e:
                self.logger.error(f"Unexpected error on attempt {attempt + 1}: {e}")
                return {"error": f"Unexpected error: {str(e)}"}

        return {"error": "Maximum retry attempts exceeded"}

    async def make_async_request(self, method: str, endpoint: str, data: Dict = None) -> Dict:
        """Make async HTTP request to Unipile API"""
        url = f"{self.base_url}/{endpoint.lstrip('/')}"

        async with aiohttp.ClientSession() as session:
            for attempt in range(self.max_retries + 1):
                try:
                    if method.upper() == "GET":
                        async with session.get(url, headers=self.headers) as response:
                            response.raise_for_status()
                            return await response.json() if response.content_length else {"success": True}
                    elif method.upper() == "POST":
                        async with session.post(url, headers=self.headers, json=data) as response:
                            response.raise_for_status()
                            return await response.json() if response.content_length else {"success": True}
                    else:
                        raise ValueError(f"Unsupported HTTP method: {method}")

                except asyncio.TimeoutError:
                    self.logger.warning(f"Async request timeout on attempt {attempt + 1}")
                    if attempt < self.max_retries:
                        await asyncio.sleep(self.retry_delay * (2 ** attempt))
                        continue
                    return {"error": f"Async request timeout after {self.max_retries + 1} attempts"}

                except Exception as e:
                    self.logger.warning(f"Async request failed on attempt {attempt + 1}: {e}")
                    if attempt < self.max_retries:
                        await asyncio.sleep(self.retry_delay * (2 ** attempt))
                        continue
                    return {"error": f"Async request failed after {self.max_retries + 1} attempts: {str(e)}"}

        return {"error": "Maximum async retry attempts exceeded"}

class TelegramMessaging:
    def __init__(self, bot_token: str = None, unipile_api_key: str = "K0Py2YdG.SazddZm5laRo0Bk9kZ0cKnNt8PLt/AJj15NEGsM7lrk="):
        """Initialize enhanced Telegram messaging with both bot token and Unipile API"""
        self.bot_token = bot_token
        self.unipile_api_key = unipile_api_key

        # Load configuration first
        self.config = self._load_config()

        # Initialize Unipile client with retry settings from config
        retry_settings = self.config.get("retry_settings", {})
        self.unipile = UnipileClient(
            unipile_api_key,
            max_retries=retry_settings.get("max_retries", 3),
            retry_delay=retry_settings.get("retry_delay", 1.0)
        )

        # Telegram Bot API setup
        self.api_url = "https://api.telegram.org/bot"
        self.base_url = f"{self.api_url}{self.bot_token}" if self.bot_token else ""

        # Bot configuration
        self.bot_info = None
        self.connection_status = ConnectionStatus.DISCONNECTED
        self.last_connection_check = None

        # Enhanced rate limiting configuration
        rate_limit_config = self.config.get("rate_limit", {})
        self.rate_limit = RateLimitConfig(
            messages_per_second=rate_limit_config.get("messages_per_second", 30),
            messages_per_minute=rate_limit_config.get("messages_per_minute", 20),
            messages_per_chat_per_second=rate_limit_config.get("messages_per_chat_per_second", 1),
            burst_limit=rate_limit_config.get("burst_limit", 5)
        )

        # Rate limiting tracking
        self.last_request_time = 0
        self.min_request_interval = 1 / self.rate_limit.messages_per_second
        self.message_count_per_minute = []
        self.chat_last_message_time = {}

        # Setup enhanced logging
        log_level = self.config.get("settings", {}).get("log_level", "INFO")
        logging.basicConfig(
            level=getattr(logging, log_level),
            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
        )
        self.logger = logging.getLogger(__name__)

        # Message validation settings
        self.max_message_length = self.config.get("settings", {}).get("max_message_length", 4096)
        self.auto_retry = self.config.get("settings", {}).get("auto_retry", True)
        self.max_retries = self.config.get("settings", {}).get("max_retries", 3)

        # Check for existing bot configuration
        if self.bot_token and self.bot_token != "":
            self._check_bot_status()
        else:
            # Try to load bot token from config
            if not self.bot_token and self.config.get("bot_token"):
                self.bot_token = self.config["bot_token"]
                self.base_url = f"{self.api_url}{self.bot_token}"
                self._check_bot_status()
            else:
                self.connection_status = ConnectionStatus.NOT_CONFIGURED

    def _load_config(self) -> Dict:
        """Load configuration from JSON file"""
        config_path = get_config_path()
        try:
            with open(config_path, 'r') as f:
                config = json.load(f)
                # Update bot token from config if not provided
                if not self.bot_token and config.get("bot_token"):
                    self.bot_token = config["bot_token"]
                    # Update base_url if api_url is already set
                    if hasattr(self, 'api_url'):
                        self.base_url = f"{self.api_url}{self.bot_token}"
                return config
        except FileNotFoundError:
            self.logger.warning(f"Config file not found: {config_path}")
            return {}
        except json.JSONDecodeError:
            self.logger.error(f"Invalid JSON in config file: {config_path}")
            return {}

    def _save_config(self):
        """Save configuration to JSON file"""
        config_path = get_config_path()
        try:
            self.config["bot_token"] = self.bot_token
            with open(config_path, 'w') as f:
                json.dump(self.config, f, indent=2)
        except Exception as e:
            self.logger.error(f"Error saving config: {e}")

    def _check_bot_status(self):
        """Enhanced bot status checking with better error handling"""
        try:
            if self.bot_token:
                self.connection_status = ConnectionStatus.CONNECTING
                bot_info = self.get_me()

                if bot_info.get("ok"):
                    self.bot_info = bot_info["result"]
                    self.connection_status = ConnectionStatus.CONNECTED
                    self.last_connection_check = datetime.now()
                    self.logger.info(f"Bot connected: @{self.bot_info.get('username')} (ID: {self.bot_info.get('id')})")
                else:
                    self.connection_status = ConnectionStatus.ERROR
                    error_code = bot_info.get("error_code")
                    description = bot_info.get("description", "Unknown error")

                    # Only log detailed error if it's not a 404 (invalid token) or 401 (unauthorized)
                    if error_code not in [404, 401]:
                        self.logger.error(f"Bot connection failed: {description} (Code: {error_code})")
                    elif error_code == 401:
                        self.logger.warning("Bot token is invalid or expired")
                    else:
                        self.logger.debug(f"Bot connection check failed: {description}")
            else:
                self.connection_status = ConnectionStatus.NOT_CONFIGURED

        except Exception as e:
            # Only log error if it's not related to invalid tokens
            if "404" not in str(e) and "401" not in str(e):
                self.logger.error(f"Error checking bot status: {e}")
            self.connection_status = ConnectionStatus.ERROR

    def setup_bot(self) -> Dict:
        """Configure Telegram bot"""
        try:
            if not self.bot_token:
                return {"error": "Bot token not provided"}

            # Get bot information
            bot_info = self.get_me()
            if not bot_info.get("ok"):
                return {"error": f"Invalid bot token: {bot_info.get('description', 'Unknown error')}"}

            self.bot_info = bot_info["result"]
            self.connection_status = "connected"

            # Save configuration
            self._save_config()

            return {
                "success": True,
                "bot_info": self.bot_info,
                "status": "configured",
                "message": f"Bot @{self.bot_info.get('username')} configured successfully"
            }

        except Exception as e:
            self.logger.error(f"Bot setup error: {e}")
            return {"error": str(e)}

    def _enhanced_rate_limit(self, chat_id: str = None):
        """Enhanced rate limiting with per-chat and global limits"""
        current_time = time.time()

        # Global rate limiting
        time_since_last_request = current_time - self.last_request_time
        if time_since_last_request < self.min_request_interval:
            sleep_time = self.min_request_interval - time_since_last_request
            time.sleep(sleep_time)

        # Per-minute rate limiting
        self.message_count_per_minute = [
            timestamp for timestamp in self.message_count_per_minute
            if current_time - timestamp < 60
        ]

        if len(self.message_count_per_minute) >= self.rate_limit.messages_per_minute:
            sleep_time = 60 - (current_time - self.message_count_per_minute[0])
            if sleep_time > 0:
                self.logger.info(f"Rate limit reached, sleeping for {sleep_time:.2f} seconds")
                time.sleep(sleep_time)

        # Per-chat rate limiting
        if chat_id:
            chat_last_time = self.chat_last_message_time.get(chat_id, 0)
            chat_interval = 1 / self.rate_limit.messages_per_chat_per_second
            time_since_chat_message = current_time - chat_last_time

            if time_since_chat_message < chat_interval:
                sleep_time = chat_interval - time_since_chat_message
                time.sleep(sleep_time)

            self.chat_last_message_time[chat_id] = time.time()

        # Update tracking
        self.last_request_time = time.time()
        self.message_count_per_minute.append(self.last_request_time)

    def _rate_limit(self):
        """Legacy rate limiting method for backward compatibility"""
        self._enhanced_rate_limit()

    def _make_request(self, method: str, endpoint: str, data: Dict = None, files: Dict = None,
                     chat_id: str = None, retry_count: int = 0) -> Dict:
        """Enhanced HTTP request to Telegram Bot API with retry logic and better error handling"""
        if not self.bot_token:
            return {"error": "Bot token not configured", "ok": False}

        # Apply enhanced rate limiting
        self._enhanced_rate_limit(chat_id)

        url = f"{self.base_url}/{endpoint}"
        max_retries = self.max_retries if self.auto_retry else 0

        for attempt in range(max_retries + 1):
            try:
                # Make the HTTP request
                if method.upper() == "GET":
                    response = requests.get(url, params=data, timeout=30)
                elif method.upper() == "POST":
                    if files:
                        response = requests.post(url, data=data, files=files, timeout=30)
                    else:
                        response = requests.post(url, json=data, timeout=30)
                else:
                    raise ValueError(f"Unsupported HTTP method: {method}")

                # Parse response
                try:
                    response_data = response.json()
                except json.JSONDecodeError:
                    response_data = {"error": f"Invalid JSON response: {response.text[:200]}"}

                # Check if request was successful
                if response.status_code == 200 and response_data.get("ok"):
                    if attempt > 0:
                        self.logger.info(f"Request succeeded on attempt {attempt + 1}")
                    return response_data

                # Handle specific Telegram API errors
                error_code = response_data.get("error_code")
                description = response_data.get("description", "")

                # Check if this is a retryable error
                retryable_errors = [429, 500, 502, 503, 504]  # Rate limit, server errors
                if error_code in retryable_errors and attempt < max_retries:
                    retry_delay = self.config.get("settings", {}).get("retry_delay", 1)
                    if error_code == 429:  # Rate limit
                        # Extract retry_after from response if available
                        retry_after = response_data.get("parameters", {}).get("retry_after", retry_delay)
                        self.logger.warning(f"Rate limited, waiting {retry_after} seconds")
                        time.sleep(retry_after)
                    else:
                        # Exponential backoff for other errors
                        sleep_time = retry_delay * (2 ** attempt)
                        self.logger.warning(f"Retryable error {error_code}, attempt {attempt + 1}, waiting {sleep_time}s")
                        time.sleep(sleep_time)
                    continue

                # Non-retryable error or max retries reached
                error_msg = f"HTTP {response.status_code}: {response.reason}"
                if description:
                    error_msg += f" - {description}"
                if error_code:
                    error_msg += f" (Error code: {error_code})"

                # Only log error if it's not a 404 (invalid token) or 400 (bad request)
                if response.status_code not in [404, 400]:
                    self.logger.error(f"Telegram API error: {error_msg}")
                    if attempt == max_retries:
                        self.logger.error(f"Request URL: {url}")
                        self.logger.error(f"Request data: {data}")

                return {
                    "error": error_msg,
                    "telegram_error": response_data,
                    "status_code": response.status_code,
                    "ok": False,
                    "retry_count": attempt
                }

            except requests.exceptions.Timeout:
                if attempt < max_retries:
                    sleep_time = self.config.get("settings", {}).get("retry_delay", 1) * (2 ** attempt)
                    self.logger.warning(f"Request timeout, attempt {attempt + 1}, retrying in {sleep_time}s")
                    time.sleep(sleep_time)
                    continue
                return {"error": f"Request timeout after {max_retries + 1} attempts", "ok": False}

            except requests.exceptions.RequestException as e:
                if attempt < max_retries:
                    sleep_time = self.config.get("settings", {}).get("retry_delay", 1) * (2 ** attempt)
                    self.logger.warning(f"Request failed, attempt {attempt + 1}: {e}, retrying in {sleep_time}s")
                    time.sleep(sleep_time)
                    continue
                self.logger.error(f"Request failed after {max_retries + 1} attempts: {e}")
                return {"error": str(e), "ok": False}

            except Exception as e:
                self.logger.error(f"Unexpected error in request: {e}")
                return {"error": f"Unexpected error: {str(e)}", "ok": False}

        return {"error": "Maximum retry attempts exceeded", "ok": False}

    def get_me(self) -> Dict:
        """Get basic information about the bot"""
        return self._make_request("GET", "getMe")

    def send_message(self, user_id: Union[int, str], message: str, parse_mode: str = "HTML",
                    disable_web_page_preview: bool = False, disable_notification: bool = False,
                    reply_to_message_id: int = None, **kwargs) -> MessageResult:
        """Enhanced send direct message to user with validation and formatting"""
        start_time = datetime.now()

        try:
            # Validate and sanitize input
            if not message or not isinstance(message, str):
                return MessageResult(
                    success=False,
                    error="Message content is required and must be a string",
                    timestamp=start_time
                )

            # Sanitize message content
            sanitized_message = sanitize_message(message, self.max_message_length)
            if not sanitized_message:
                return MessageResult(
                    success=False,
                    error="Message content is empty after sanitization",
                    timestamp=start_time
                )

            # Validate user_id
            if not user_id:
                return MessageResult(
                    success=False,
                    error="User ID is required",
                    timestamp=start_time
                )

            # Convert user_id to string for consistency
            user_id_str = str(user_id)

            self.logger.info(f"Sending message to {user_id_str} (length: {len(sanitized_message)})")

            # Try Unipile API first if available
            self.logger.debug("Attempting to send message via Unipile API...")
            unipile_result = self._send_via_unipile(user_id_str, sanitized_message)
            if unipile_result.get("success"):
                self.logger.info(f"Message sent successfully via Unipile to {user_id_str}")
                return MessageResult(
                    success=True,
                    message_id=unipile_result.get("message_id"),
                    platform="telegram",
                    method="unipile",
                    timestamp=start_time
                )
            else:
                self.logger.warning(f"Unipile failed: {unipile_result.get('error')}, trying bot API fallback...")

            # Fallback to direct Telegram Bot API
            if self.connection_status != ConnectionStatus.CONNECTED:
                self.logger.info("Bot not connected, attempting to establish connection...")
                connection_test = self.test_connection()
                if not connection_test.get("success"):
                    return MessageResult(
                        success=False,
                        error="Both Unipile and Bot API failed. Please connect a Telegram account via Unipile or configure bot token.",
                        timestamp=start_time
                    )

            # Prepare request data
            data = {
                "chat_id": user_id_str,
                "text": sanitized_message,
                "parse_mode": parse_mode
            }

            # Add optional parameters
            if disable_web_page_preview:
                data["disable_web_page_preview"] = True
            if disable_notification:
                data["disable_notification"] = True
            if reply_to_message_id:
                data["reply_to_message_id"] = reply_to_message_id

            # Make the request
            result = self._make_request("POST", "sendMessage", data, chat_id=user_id_str)

            if result.get("ok"):
                self.logger.info(f"Message sent successfully via Bot API to user {user_id_str}")
                return MessageResult(
                    success=True,
                    message_id=str(result["result"]["message_id"]),
                    platform="telegram",
                    method="bot_api",
                    timestamp=start_time,
                    retry_count=result.get("retry_count", 0)
                )
            else:
                error_msg = result.get("description", result.get("error", "Unknown error"))
                self.logger.error(f"Failed to send message via Bot API: {error_msg}")
                return MessageResult(
                    success=False,
                    error=f"Both Unipile and Bot API failed. Unipile: {unipile_result.get('error')}, Bot API: {error_msg}",
                    timestamp=start_time,
                    retry_count=result.get("retry_count", 0)
                )

        except Exception as e:
            self.logger.error(f"Message sending error: {e}")
            return MessageResult(
                success=False,
                error=str(e),
                timestamp=start_time
            )

    async def send_message_async(self, user_id: Union[int, str], message: str,
                               parse_mode: str = "HTML", **kwargs) -> MessageResult:
        """Async version of send_message for better performance"""
        start_time = datetime.now()

        try:
            # Validate and sanitize input (same as sync version)
            if not message or not isinstance(message, str):
                return MessageResult(
                    success=False,
                    error="Message content is required and must be a string",
                    timestamp=start_time
                )

            sanitized_message = sanitize_message(message, self.max_message_length)
            if not sanitized_message:
                return MessageResult(
                    success=False,
                    error="Message content is empty after sanitization",
                    timestamp=start_time
                )

            user_id_str = str(user_id)
            self.logger.info(f"Async sending message to {user_id_str}")

            # Try Unipile API first (async)
            unipile_result = await self.unipile.make_async_request(
                "POST", "chats/messages",
                {
                    "to": user_id_str,
                    "text": sanitized_message,
                    "type": "text"
                }
            )

            if not unipile_result.get("error"):
                return MessageResult(
                    success=True,
                    message_id=unipile_result.get("id"),
                    platform="telegram",
                    method="unipile_async",
                    timestamp=start_time
                )

            # Fallback to sync bot API (could be enhanced to async in future)
            return self.send_message(user_id, message, parse_mode, **kwargs)

        except Exception as e:
            self.logger.error(f"Async message sending error: {e}")
            return MessageResult(
                success=False,
                error=str(e),
                timestamp=start_time
            )

    def send_group_message(self, group_id: Union[int, str], message: str, parse_mode: str = "HTML") -> Dict:
        """Send message to group"""
        # Check bot connection status first, and try to connect if not connected
        if self.connection_status != "connected":
            self.logger.info("Bot not connected, attempting to establish connection...")
            connection_test = self.test_connection()
            if not connection_test.get("success"):
                return {
                    "error": "Bot not connected. Please configure bot token first.",
                    "status": self.connection_status,
                    "connection_error": connection_test.get("error")
                }

        try:
            data = {
                "chat_id": group_id,
                "text": message,
                "parse_mode": parse_mode
            }

            result = self._make_request("POST", "sendMessage", data)

            if result.get("ok"):
                self.logger.info(f"Message sent successfully to group {group_id}")
                return {
                    "success": True,
                    "message_id": result["result"]["message_id"],
                    "to": group_id,
                    "status": "sent",
                    "ok": True
                }
            else:
                error_msg = result.get("description", result.get("error", "Unknown error"))
                self.logger.error(f"Failed to send group message: {error_msg}")
                return {
                    "error": error_msg,
                    "telegram_error": result,
                    "ok": False
                }

        except Exception as e:
            self.logger.error(f"Group message sending error: {e}")
            return {"error": str(e), "ok": False}

    def _send_via_unipile(self, chat_id: Union[int, str], message: str) -> Dict:
        """Send message via Unipile API (if Telegram account is connected)"""
        try:
            # Check if Telegram account is connected via Unipile
            accounts = self.unipile.make_request("GET", "accounts")

            telegram_account = None
            if "items" in accounts:
                for account in accounts["items"]:
                    if account.get("type") == "TELEGRAM":
                        telegram_account = account
                        break

            if not telegram_account:
                return {"error": "No Telegram account connected via Unipile"}

            # Send message via Unipile
            message_data = {
                "account_id": telegram_account["id"],
                "to": str(chat_id),
                "text": message,
                "type": "text"
            }

            result = self.unipile.make_request("POST", "chats/messages", message_data)

            if "error" not in result:
                self.logger.info(f"Message sent via Unipile to {chat_id}")
                return {
                    "success": True,
                    "message_id": result.get("id"),
                    "to": chat_id,
                    "status": "sent",
                    "via": "unipile"
                }
            else:
                return {"error": result["error"]}

        except Exception as e:
            self.logger.error(f"Unipile message sending error: {e}")
            return {"error": str(e)}

    def send_bulk_messages(self, recipients: List[Union[int, str]], message: str,
                          delay: float = 1.0, max_concurrent: int = 5) -> List[Dict]:
        """Enhanced bulk message sending with better error handling and progress tracking"""
        if not recipients:
            return []

        # Validate message
        if not message or not isinstance(message, str):
            return [{
                "chat_id": "all",
                "result": MessageResult(success=False, error="Invalid message content"),
                "timestamp": datetime.now().isoformat()
            }]

        results = []
        total_recipients = len(recipients)
        successful_count = 0
        failed_count = 0

        self.logger.info(f"Starting bulk message send to {total_recipients} recipients")

        for i, chat_id in enumerate(recipients, 1):
            try:
                # Send message
                result = self.send_message(chat_id, message)

                # Track success/failure
                if result.success:
                    successful_count += 1
                else:
                    failed_count += 1

                results.append({
                    "chat_id": str(chat_id),
                    "result": result.__dict__ if hasattr(result, '__dict__') else result,
                    "timestamp": datetime.now().isoformat(),
                    "sequence": i,
                    "progress": f"{i}/{total_recipients}"
                })

                # Progress logging
                if i % 10 == 0 or i == total_recipients:
                    self.logger.info(f"Bulk progress: {i}/{total_recipients} ({successful_count} success, {failed_count} failed)")

                # Add delay between messages (respects rate limiting)
                if delay > 0 and i < total_recipients:
                    time.sleep(delay)

            except Exception as e:
                failed_count += 1
                self.logger.error(f"Bulk message error for {chat_id}: {e}")
                results.append({
                    "chat_id": str(chat_id),
                    "result": MessageResult(success=False, error=str(e)).__dict__,
                    "timestamp": datetime.now().isoformat(),
                    "sequence": i,
                    "progress": f"{i}/{total_recipients}"
                })

        # Final summary
        self.logger.info(f"Bulk messaging completed: {total_recipients} total, {successful_count} successful, {failed_count} failed")

        return results

    async def send_bulk_messages_async(self, recipients: List[Union[int, str]], message: str,
                                     max_concurrent: int = 5) -> List[Dict]:
        """Async bulk message sending for better performance"""
        if not recipients:
            return []

        self.logger.info(f"Starting async bulk message send to {len(recipients)} recipients")

        # Create semaphore to limit concurrent requests
        semaphore = asyncio.Semaphore(max_concurrent)

        async def send_single_async(chat_id, sequence):
            async with semaphore:
                try:
                    result = await self.send_message_async(chat_id, message)
                    return {
                        "chat_id": str(chat_id),
                        "result": result.__dict__,
                        "timestamp": datetime.now().isoformat(),
                        "sequence": sequence
                    }
                except Exception as e:
                    return {
                        "chat_id": str(chat_id),
                        "result": MessageResult(success=False, error=str(e)).__dict__,
                        "timestamp": datetime.now().isoformat(),
                        "sequence": sequence
                    }

        # Create tasks for all recipients
        tasks = [send_single_async(chat_id, i+1) for i, chat_id in enumerate(recipients)]

        # Execute all tasks concurrently
        results = await asyncio.gather(*tasks, return_exceptions=True)

        # Process results and handle exceptions
        processed_results = []
        successful_count = 0
        failed_count = 0

        for result in results:
            if isinstance(result, Exception):
                failed_count += 1
                processed_results.append({
                    "chat_id": "unknown",
                    "result": MessageResult(success=False, error=str(result)).__dict__,
                    "timestamp": datetime.now().isoformat()
                })
            else:
                if result["result"]["success"]:
                    successful_count += 1
                else:
                    failed_count += 1
                processed_results.append(result)

        self.logger.info(f"Async bulk messaging completed: {len(recipients)} total, {successful_count} successful, {failed_count} failed")
        return processed_results

    def send_photo(self, chat_id: Union[int, str], photo: str, caption: str = None, parse_mode: str = "HTML") -> Dict:
        """Send photo to chat"""
        if self.connection_status != "connected":
            return {"error": "Bot not connected"}

        data = {
            "chat_id": chat_id,
            "photo": photo,
            "parse_mode": parse_mode
        }

        if caption:
            data["caption"] = caption

        result = self._make_request("POST", "sendPhoto", data)

        if result.get("ok"):
            self.logger.info(f"Photo sent successfully to chat {chat_id}")
            return {"success": True, "message_id": result["result"]["message_id"]}
        else:
            self.logger.error(f"Failed to send photo: {result}")
            return {"error": result.get("description", "Unknown error")}

    def get_updates(self, offset: int = None, limit: int = 100, timeout: int = 0) -> Dict:
        """Get updates from Telegram"""
        data = {
            "limit": limit,
            "timeout": timeout
        }

        if offset:
            data["offset"] = offset

        return self._make_request("GET", "getUpdates", data)

    def process_updates(self, updates: List[Dict]) -> List[Dict]:
        """Process incoming updates and generate responses"""
        responses = []

        for update in updates:
            try:
                if "message" in update:
                    message = update["message"]
                    chat_id = message["chat"]["id"]
                    user_name = message.get("from", {}).get("first_name", "User")
                    text = message.get("text", "")

                    # Log incoming message
                    self.logger.info(f"Received message from {user_name} (ID: {chat_id}): {text}")

                    # Generate response based on message content
                    response_text = self._generate_response(text, user_name, message)

                    if response_text:
                        # Send response
                        result = self.send_message(chat_id, response_text)
                        responses.append({
                            "update_id": update["update_id"],
                            "chat_id": chat_id,
                            "user_name": user_name,
                            "incoming_message": text,
                            "response": response_text,
                            "sent_successfully": result.success if hasattr(result, 'success') else result.get("ok", False),
                            "timestamp": datetime.now().isoformat()
                        })

            except Exception as e:
                self.logger.error(f"Error processing update {update.get('update_id', 'unknown')}: {e}")
                responses.append({
                    "update_id": update.get("update_id", "unknown"),
                    "error": str(e),
                    "timestamp": datetime.now().isoformat()
                })

        return responses

    def _generate_response(self, text: str, user_name: str, message: Dict) -> str:
        """Generate appropriate response based on incoming message"""
        if not text:
            return None

        text_lower = text.lower().strip()

        # Handle common commands and greetings
        if text_lower in ["/start", "start", "hello", "hi", "hey"]:
            return f"👋 Hello {user_name}! Welcome to our bot. How can I help you today?\n\nYou can try:\n• Ask me anything\n• Type 'help' for more options\n• Send 'status' to check bot status"

        elif text_lower in ["help", "/help"]:
            return """🤖 Bot Help Menu:

📝 Commands:
• /start - Start conversation
• help - Show this help menu
• status - Check bot status
• echo [message] - Echo your message
• time - Get current time
• info - Get bot information

💬 You can also just chat with me naturally!"""

        elif text_lower in ["status", "/status"]:
            health = self.get_connection_health()
            return f"""📊 Bot Status:
• Status: {health['status']}
• Messages sent this minute: {health['message_stats']['messages_sent_this_minute']}
• Active chats: {health['message_stats']['active_chats']}
• Last check: {datetime.now().strftime('%H:%M:%S')}"""

        elif text_lower.startswith("echo "):
            echo_text = text[5:].strip()
            return f"🔄 You said: {echo_text}"

        elif text_lower in ["time", "/time"]:
            current_time = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            return f"🕐 Current time: {current_time}"

        elif text_lower in ["info", "/info"]:
            if self.bot_info:
                return f"""🤖 Bot Information:
• Name: {self.bot_info.get('first_name', 'Unknown')}
• Username: @{self.bot_info.get('username', 'unknown')}
• ID: {self.bot_info.get('id', 'unknown')}
• Can join groups: {self.bot_info.get('can_join_groups', False)}
• Supports inline: {self.bot_info.get('supports_inline_queries', False)}"""
            else:
                return "🤖 Bot information not available."

        # Handle questions and general conversation
        elif "?" in text:
            return f"🤔 That's an interesting question, {user_name}! I'm a simple bot, but I'm here to help. Try asking me for 'help' to see what I can do!"

        elif any(word in text_lower for word in ["thank", "thanks", "thx"]):
            return f"😊 You're welcome, {user_name}! Happy to help!"

        elif any(word in text_lower for word in ["bye", "goodbye", "see you"]):
            return f"👋 Goodbye, {user_name}! Feel free to message me anytime!"

        # Default response for unrecognized messages
        else:
            return f"💬 Hi {user_name}! I received your message: \"{text}\"\n\nI'm a simple bot. Type 'help' to see what I can do!"

    def start_polling(self, interval: float = 1.0, max_iterations: int = None) -> None:
        """Start polling for updates and responding to messages"""
        self.logger.info("Starting message polling...")

        if self.connection_status != ConnectionStatus.CONNECTED:
            self.logger.error("Bot not connected. Cannot start polling.")
            return

        offset = None
        iteration = 0

        try:
            while True:
                if max_iterations and iteration >= max_iterations:
                    break

                # Get updates
                updates_result = self.get_updates(offset=offset, timeout=int(interval))

                if updates_result.get("ok") and updates_result.get("result"):
                    updates = updates_result["result"]

                    if updates:
                        self.logger.info(f"Processing {len(updates)} updates...")

                        # Process updates and send responses
                        responses = self.process_updates(updates)

                        # Update offset to avoid processing same messages
                        offset = updates[-1]["update_id"] + 1

                        # Log processing results
                        successful_responses = sum(1 for r in responses if r.get("sent_successfully", False))
                        self.logger.info(f"Processed {len(updates)} updates, sent {successful_responses} responses")

                # Wait before next poll
                time.sleep(interval)
                iteration += 1

        except KeyboardInterrupt:
            self.logger.info("Polling stopped by user")
        except Exception as e:
            self.logger.error(f"Error during polling: {e}")

    async def start_async_polling(self, interval: float = 1.0, max_iterations: int = None) -> None:
        """Async version of message polling"""
        self.logger.info("Starting async message polling...")

        if self.connection_status != ConnectionStatus.CONNECTED:
            self.logger.error("Bot not connected. Cannot start polling.")
            return

        offset = None
        iteration = 0

        try:
            while True:
                if max_iterations and iteration >= max_iterations:
                    break

                # Get updates (using sync method for now, could be made async)
                updates_result = self.get_updates(offset=offset, timeout=int(interval))

                if updates_result.get("ok") and updates_result.get("result"):
                    updates = updates_result["result"]

                    if updates:
                        self.logger.info(f"Async processing {len(updates)} updates...")

                        # Process updates
                        responses = self.process_updates(updates)
                        offset = updates[-1]["update_id"] + 1

                        successful_responses = sum(1 for r in responses if r.get("sent_successfully", False))
                        self.logger.info(f"Async processed {len(updates)} updates, sent {successful_responses} responses")

                await asyncio.sleep(interval)
                iteration += 1

        except Exception as e:
            self.logger.error(f"Error during async polling: {e}")



    def get_bot_info(self) -> Dict:
        """Get bot information"""
        if self.bot_info:
            return {
                "success": True,
                "bot_info": self.bot_info,
                "status": self.connection_status
            }
        else:
            bot_info = self.get_me()
            if bot_info.get("ok"):
                self.bot_info = bot_info["result"]
                return {
                    "success": True,
                    "bot_info": self.bot_info,
                    "status": "connected"
                }
            else:
                return {"error": bot_info.get("description", "Failed to get bot info")}

    def is_configured(self) -> bool:
        """Check if Telegram bot is properly configured"""
        return bool(self.bot_token) and self.connection_status == "connected"

    def test_connection(self) -> Dict:
        """Enhanced connection test with comprehensive status reporting"""
        test_start = datetime.now()

        try:
            if not self.bot_token:
                return {
                    "success": False,
                    "error": "Bot token not configured",
                    "status": ConnectionStatus.NOT_CONFIGURED.value,
                    "timestamp": test_start.isoformat(),
                    "test_duration_ms": 0
                }

            self.logger.info("Testing bot connection...")
            self.connection_status = ConnectionStatus.CONNECTING

            # Test connection by getting bot info
            bot_info = self.get_me()
            test_duration = (datetime.now() - test_start).total_seconds() * 1000

            if bot_info.get("ok"):
                self.bot_info = bot_info["result"]
                self.connection_status = ConnectionStatus.CONNECTED
                self.last_connection_check = datetime.now()

                # Additional connection health checks
                health_info = {
                    "can_read_messages": True,  # If getMe works, basic API access is working
                    "bot_username": self.bot_info.get("username"),
                    "bot_id": self.bot_info.get("id"),
                    "bot_name": self.bot_info.get("first_name"),
                    "supports_inline_queries": self.bot_info.get("supports_inline_queries", False),
                    "can_join_groups": self.bot_info.get("can_join_groups", False),
                    "can_read_all_group_messages": self.bot_info.get("can_read_all_group_messages", False)
                }

                return {
                    "success": True,
                    "message": f"Bot @{self.bot_info.get('username')} is connected and working",
                    "bot_info": self.bot_info,
                    "health_info": health_info,
                    "status": ConnectionStatus.CONNECTED.value,
                    "timestamp": test_start.isoformat(),
                    "test_duration_ms": round(test_duration, 2),
                    "last_check": self.last_connection_check.isoformat() if self.last_connection_check else None
                }
            else:
                self.connection_status = ConnectionStatus.ERROR
                error_code = bot_info.get("error_code")
                description = bot_info.get("description", "Connection test failed")

                return {
                    "success": False,
                    "error": description,
                    "error_code": error_code,
                    "status": ConnectionStatus.ERROR.value,
                    "timestamp": test_start.isoformat(),
                    "test_duration_ms": round(test_duration, 2),
                    "telegram_error": bot_info
                }

        except Exception as e:
            test_duration = (datetime.now() - test_start).total_seconds() * 1000
            self.logger.error(f"Connection test error: {e}")
            self.connection_status = ConnectionStatus.ERROR

            return {
                "success": False,
                "error": str(e),
                "status": ConnectionStatus.ERROR.value,
                "timestamp": test_start.isoformat(),
                "test_duration_ms": round(test_duration, 2)
            }

    def get_connection_health(self) -> Dict:
        """Get comprehensive connection health information"""
        return {
            "status": self.connection_status.value if isinstance(self.connection_status, ConnectionStatus) else self.connection_status,
            "bot_configured": bool(self.bot_token),
            "bot_info": self.bot_info,
            "last_connection_check": self.last_connection_check.isoformat() if self.last_connection_check else None,
            "unipile_configured": bool(self.unipile_api_key),
            "rate_limit_config": {
                "messages_per_second": self.rate_limit.messages_per_second,
                "messages_per_minute": self.rate_limit.messages_per_minute,
                "messages_per_chat_per_second": self.rate_limit.messages_per_chat_per_second
            },
            "message_stats": {
                "messages_sent_this_minute": len(self.message_count_per_minute),
                "active_chats": len(self.chat_last_message_time)
            },
            "timestamp": datetime.now().isoformat()
        }

    def get_unipile_accounts(self) -> Dict:
        """Get Telegram accounts connected via Unipile"""
        try:
            accounts = self.unipile.make_request("GET", "accounts")

            if "error" in accounts:
                return {"error": accounts["error"]}

            # Filter for Telegram accounts
            all_accounts = accounts.get("items", [])
            telegram_accounts = [acc for acc in all_accounts if acc.get("type") == "TELEGRAM"]

            return {
                "success": True,
                "accounts": telegram_accounts,
                "total_accounts": len(all_accounts),
                "telegram_accounts": len(telegram_accounts),
                "connected": len(telegram_accounts) > 0
            }

        except Exception as e:
            self.logger.error(f"Error getting Unipile accounts: {e}")
            return {"error": str(e)}

    def update_config(self, bot_token: str = None) -> Dict:
        """Update bot configuration"""
        try:
            if bot_token:
                self.bot_token = bot_token
                self.base_url = f"{self.api_url}{self.bot_token}"
                # Re-check bot status
                self._check_bot_status()

            self._save_config()

            return {
                "success": True,
                "message": "Configuration updated successfully",
                "status": self.connection_status
            }

        except Exception as e:
            self.logger.error(f"Config update error: {e}")
            return {"error": str(e)}

# Legacy TelegramAPI class for backward compatibility
class TelegramAPI:
    def __init__(self, config_path: str = None):
        """Initialize Telegram API client"""
        # Use the same path resolution as the new TelegramMessaging class
        self.config_path = config_path if config_path else get_config_path()
        self.config = self._load_config()
        self.bot_token = self.config.get("bot_token")
        self.api_url = self.config.get("api_url", "https://api.telegram.org/bot")
        self.base_url = f"{self.api_url}{self.bot_token}" if self.bot_token else ""

        # Rate limiting
        self.last_request_time = 0
        self.min_request_interval = 1 / self.config.get("rate_limit", {}).get("messages_per_second", 30)

        # Setup logging
        logging.basicConfig(level=logging.INFO)
        self.logger = logging.getLogger(__name__)
    
    def _load_config(self) -> Dict:
        """Load configuration from JSON file"""
        try:
            with open(self.config_path, 'r') as f:
                return json.load(f)
        except FileNotFoundError:
            self.logger.warning(f"Config file not found: {self.config_path}")
            return {}
        except json.JSONDecodeError:
            self.logger.error(f"Invalid JSON in config file: {self.config_path}")
            return {}
    
    def _save_config(self):
        """Save configuration to JSON file"""
        try:
            with open(self.config_path, 'w') as f:
                json.dump(self.config, f, indent=2)
        except Exception as e:
            self.logger.error(f"Error saving config: {e}")
    
    def _rate_limit(self):
        """Implement rate limiting"""
        current_time = time.time()
        time_since_last_request = current_time - self.last_request_time
        
        if time_since_last_request < self.min_request_interval:
            sleep_time = self.min_request_interval - time_since_last_request
            time.sleep(sleep_time)
        
        self.last_request_time = time.time()
    
    def _make_request(self, method: str, endpoint: str, data: Dict = None, files: Dict = None) -> Dict:
        """Make HTTP request with error handling"""
        if not self.bot_token:
            return {"error": "Bot token not configured"}
        
        self._rate_limit()
        
        url = f"{self.base_url}/{endpoint}"
        
        try:
            if method.upper() == "GET":
                response = requests.get(url, params=data)
            elif method.upper() == "POST":
                if files:
                    response = requests.post(url, data=data, files=files)
                else:
                    response = requests.post(url, json=data)
            else:
                raise ValueError(f"Unsupported HTTP method: {method}")
            
            response.raise_for_status()
            return response.json()
            
        except requests.exceptions.RequestException as e:
            self.logger.error(f"Request failed: {e}")
            return {"error": str(e)}
    
    def get_me(self) -> Dict:
        """Get basic information about the bot"""
        return self._make_request("GET", "getMe")
    
    def send_message(self, chat_id: Union[int, str], text: str, 
                    parse_mode: str = None, reply_markup: Dict = None) -> Dict:
        """Send text message to chat"""
        if not self.bot_token:
            return {"error": "Telegram bot not configured properly"}
        
        data = {
            "chat_id": chat_id,
            "text": text,
            "parse_mode": parse_mode or self.config.get("settings", {}).get("parse_mode", "HTML")
        }
        
        if reply_markup:
            data["reply_markup"] = json.dumps(reply_markup)
        
        result = self._make_request("POST", "sendMessage", data)
        
        if result.get("ok"):
            self.logger.info(f"Message sent successfully to chat {chat_id}")
        else:
            self.logger.error(f"Failed to send message: {result}")
        
        return result
    
    def send_photo(self, chat_id: Union[int, str], photo: str, 
                  caption: str = None, parse_mode: str = None) -> Dict:
        """Send photo to chat"""
        data = {
            "chat_id": chat_id,
            "photo": photo,
            "parse_mode": parse_mode or self.config.get("settings", {}).get("parse_mode", "HTML")
        }
        
        if caption:
            data["caption"] = caption
        
        result = self._make_request("POST", "sendPhoto", data)
        
        if result.get("ok"):
            self.logger.info(f"Photo sent successfully to chat {chat_id}")
        else:
            self.logger.error(f"Failed to send photo: {result}")
        
        return result
    
    def send_document(self, chat_id: Union[int, str], document: str, 
                     caption: str = None, parse_mode: str = None) -> Dict:
        """Send document to chat"""
        data = {
            "chat_id": chat_id,
            "document": document,
            "parse_mode": parse_mode or self.config.get("settings", {}).get("parse_mode", "HTML")
        }
        
        if caption:
            data["caption"] = caption
        
        result = self._make_request("POST", "sendDocument", data)
        
        if result.get("ok"):
            self.logger.info(f"Document sent successfully to chat {chat_id}")
        else:
            self.logger.error(f"Failed to send document: {result}")
        
        return result
    
    def get_updates(self, offset: int = None, limit: int = 100, 
                   timeout: int = 0) -> Dict:
        """Get updates from Telegram"""
        data = {
            "limit": limit,
            "timeout": timeout
        }
        
        if offset:
            data["offset"] = offset
        
        return self._make_request("GET", "getUpdates", data)
    

    
    def send_bulk_messages(self, recipients: List[Union[int, str]], 
                          message: str, delay: float = 1.0) -> List[Dict]:
        """Send message to multiple recipients with delay"""
        results = []
        
        for chat_id in recipients:
            result = self.send_message(chat_id, message)
            results.append({
                "chat_id": chat_id,
                "result": result,
                "timestamp": datetime.now().isoformat()
            })
            
            if delay > 0:
                time.sleep(delay)
        
        return results
    
    def send_template_message(self, chat_id: Union[int, str], 
                            template_name: str, **kwargs) -> Dict:
        """Send predefined template message"""
        templates = self.config.get("message_templates", {})
        
        if template_name not in templates:
            return {"error": f"Template '{template_name}' not found"}
        
        message = templates[template_name].format(**kwargs)
        return self.send_message(chat_id, message)
    
    def is_configured(self) -> bool:
        """Check if Telegram bot is properly configured"""
        return bool(self.bot_token)
    
    def update_config(self, bot_token: str = None):
        """Update configuration"""
        if bot_token:
            self.config["bot_token"] = bot_token
            self.bot_token = bot_token
            self.base_url = f"{self.api_url}{self.bot_token}"

        self._save_config()
        self.logger.info("Telegram configuration updated")

# Example usage
if __name__ == "__main__":
    print("🤖 Telegram Integration Example")
    print("=" * 50)

    # Example 1: Using new TelegramMessaging class
    print("\n📱 Using TelegramMessaging class:")
    telegram = TelegramMessaging(bot_token="YOUR_BOT_TOKEN")

    # Setup bot
    setup_result = telegram.setup_bot()
    print(f"Setup result: {setup_result}")

    if telegram.is_configured():
        # Get bot info
        bot_info = telegram.get_bot_info()
        print(f"Bot info: {bot_info}")

        # Send message to user
        # result = telegram.send_message("@username", "Hello from Telegram!")
        # print(f"Message result: {result}")

        # Send message to group
        # group_result = telegram.send_group_message("-1001234567890", "Hello group!")
        # print(f"Group message result: {group_result}")

        # Send bulk messages
        # recipients = ["@user1", "@user2", "123456789"]
        # bulk_results = telegram.send_bulk_messages(recipients, "Bulk message!")
        # print(f"Bulk results: {bulk_results}")
    else:
        print("❌ Telegram bot not configured properly")

    print("\n💡 To use this integration:")
    print("1. Get a bot token from @BotFather on Telegram")
    print("2. Initialize: telegram = TelegramMessaging(bot_token='YOUR_TOKEN')")
    print("3. Setup bot: telegram.setup_bot()")
    print("4. Send messages: telegram.send_message(user_id, 'Hello!')")
    print("5. Send to groups: telegram.send_group_message(group_id, 'Hello group!')")

    # Example 2: Using legacy TelegramAPI class
    print("\n🔄 Using legacy TelegramAPI class:")
    legacy_telegram = TelegramAPI()

    if not legacy_telegram.is_configured():
        print("❌ Legacy Telegram bot not configured. Please update config.json with your bot token.")
    else:
        # Example: Get bot info
        bot_info = legacy_telegram.get_me()
        print(f"Legacy bot info: {bot_info}")

        # Example: Send a test message (replace with actual chat ID)
        # result = legacy_telegram.send_message("@username", "Hello from legacy Telegram Bot!")
        # print(f"Legacy message result: {result}")
