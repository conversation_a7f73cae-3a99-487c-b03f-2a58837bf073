"""
Telegram Bot Diagnostic Tool
Helps identify and fix common bot issues
"""

import sys
import os
import requests
import json

# Add current directory to path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_bot_locally():
    """Test bot functionality locally"""
    print("🔍 Testing Bot Locally...")
    
    try:
        from telegram_api import TelegramMessaging
        
        # Initialize bot
        bot = TelegramMessaging()
        print(f"✅ Bot initialized successfully")
        print(f"   Status: {bot.connection_status}")
        
        # Test connection
        connection = bot.test_connection()
        if connection.get("success"):
            print(f"✅ Bot connection successful")
            bot_info = connection.get("bot_info", {})
            print(f"   🤖 Bot: @{bot_info.get('username', 'unknown')}")
            print(f"   🆔 ID: {bot_info.get('id', 'unknown')}")
        else:
            print(f"❌ Bot connection failed: {connection.get('error')}")
            return False
        
        # Test getting updates
        print("\n📨 Testing message retrieval...")
        updates = bot.get_updates()
        if updates.get("ok"):
            messages = updates.get("result", [])
            print(f"✅ Retrieved {len(messages)} messages")
            
            if messages:
                print("   Recent messages:")
                for msg in messages[-3:]:  # Show last 3
                    if "message" in msg:
                        m = msg["message"]
                        user = m.get("from", {}).get("first_name", "Unknown")
                        text = m.get("text", "[Media]")
                        chat_id = m["chat"]["id"]
                        print(f"   👤 {user} ({chat_id}): {text}")
            else:
                print("   💡 No messages found. Send a message to your bot to test!")
        else:
            print(f"❌ Failed to get updates: {updates}")
            return False
        
        # Test response generation
        print("\n🤖 Testing response generation...")
        test_response = bot._generate_response("hello", "TestUser", {})
        if test_response:
            print(f"✅ Response generation works")
            print(f"   Sample response: {test_response[:100]}...")
        else:
            print("❌ Response generation failed")
        
        return True
        
    except Exception as e:
        print(f"❌ Local test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_api_endpoints():
    """Test API endpoints"""
    print("\n🌐 Testing API Endpoints...")
    
    base_url = "http://localhost:8000"
    
    endpoints_to_test = [
        ("GET", "/api/telegram/test", "Bot connection test"),
        ("GET", "/api/telegram/updates", "Get updates"),
        ("POST", "/api/telegram/process-updates", "Process updates")
    ]
    
    for method, endpoint, description in endpoints_to_test:
        try:
            url = f"{base_url}{endpoint}"
            print(f"\n🔍 Testing {description}: {method} {endpoint}")
            
            if method == "GET":
                response = requests.get(url, timeout=10)
            else:
                response = requests.post(url, timeout=10)
            
            print(f"   Status: {response.status_code}")
            
            if response.status_code == 200:
                print("   ✅ Endpoint accessible")
                try:
                    data = response.json()
                    if data.get("success"):
                        print("   ✅ Response successful")
                    else:
                        print(f"   ⚠️  Response indicates failure: {data.get('error', 'Unknown')}")
                except:
                    print("   ⚠️  Non-JSON response")
            elif response.status_code == 404:
                print("   ❌ Endpoint not found (404)")
            elif response.status_code == 500:
                print("   ❌ Server error (500)")
            else:
                print(f"   ⚠️  Unexpected status: {response.status_code}")
                
        except requests.exceptions.ConnectionError:
            print(f"   ❌ Cannot connect to {base_url}")
            print("   💡 Make sure your API server is running")
            break
        except Exception as e:
            print(f"   ❌ Error: {e}")

def find_your_chat_id():
    """Help user find their chat ID"""
    print("\n🔍 Finding Your Chat ID...")
    
    try:
        from telegram_api import TelegramMessaging
        bot = TelegramMessaging()
        
        updates = bot.get_updates()
        if updates.get("ok"):
            messages = updates.get("result", [])
            
            if messages:
                print("✅ Found recent messages. Here are the chat IDs:")
                chat_ids = set()
                
                for msg in messages:
                    if "message" in msg:
                        m = msg["message"]
                        chat_id = m["chat"]["id"]
                        user = m.get("from", {}).get("first_name", "Unknown")
                        chat_type = m["chat"].get("type", "unknown")
                        
                        if chat_id not in chat_ids:
                            print(f"   👤 {user}: {chat_id} ({chat_type})")
                            chat_ids.add(chat_id)
                
                if chat_ids:
                    print(f"\n💡 Use any of these chat IDs to send test messages!")
                    return list(chat_ids)
            else:
                print("❌ No messages found.")
                print("💡 To find your chat ID:")
                print("   1. Send any message to your bot on Telegram")
                print("   2. Run this diagnostic again")
                print("   3. Your chat ID will appear above")
        
        return []
        
    except Exception as e:
        print(f"❌ Error finding chat ID: {e}")
        return []

def test_message_sending(chat_ids):
    """Test sending messages to found chat IDs"""
    if not chat_ids:
        print("\n⚠️  No chat IDs available for testing")
        return
    
    print(f"\n📤 Testing Message Sending...")
    
    try:
        from telegram_api import TelegramMessaging
        bot = TelegramMessaging()
        
        # Test with first available chat ID
        test_chat_id = chat_ids[0]
        test_message = "🧪 This is a diagnostic test message from your bot!"
        
        print(f"   Sending test message to chat ID: {test_chat_id}")
        result = bot.send_message(test_chat_id, test_message)
        
        if hasattr(result, 'success') and result.success:
            print("   ✅ Message sent successfully!")
            print(f"   📧 Message ID: {result.message_id}")
        else:
            error = result.error if hasattr(result, 'error') else str(result)
            print(f"   ❌ Message sending failed: {error}")
            
            if "chat not found" in error.lower():
                print("   💡 This usually means:")
                print("      - The user hasn't started a conversation with the bot")
                print("      - The chat ID is incorrect")
                print("      - The bot was blocked by the user")
        
    except Exception as e:
        print(f"❌ Error testing message sending: {e}")

def main():
    """Run all diagnostics"""
    print("🤖 Telegram Bot Diagnostic Tool")
    print("=" * 50)
    
    # Test 1: Local bot functionality
    local_success = test_bot_locally()
    
    # Test 2: API endpoints
    test_api_endpoints()
    
    # Test 3: Find chat IDs
    chat_ids = find_your_chat_id()
    
    # Test 4: Message sending (if we have chat IDs)
    if local_success and chat_ids:
        test_message_sending(chat_ids)
    
    print("\n" + "=" * 50)
    print("🎯 Diagnostic Summary:")
    
    if local_success:
        print("✅ Bot is working locally")
    else:
        print("❌ Bot has local issues - check configuration")
    
    if chat_ids:
        print(f"✅ Found {len(chat_ids)} chat ID(s) for testing")
        print("💡 Next steps:")
        print("   1. Use these chat IDs in the web interface")
        print("   2. Send messages to your bot and test responses")
        print("   3. Try the bot_runner.py for automatic responses")
    else:
        print("⚠️  No chat IDs found")
        print("💡 To fix this:")
        print("   1. Open Telegram and search for your bot")
        print("   2. Send any message (like 'hello' or '/start')")
        print("   3. Run this diagnostic again")
    
    print("\n🚀 Ready to test your bot!")

if __name__ == "__main__":
    main()
