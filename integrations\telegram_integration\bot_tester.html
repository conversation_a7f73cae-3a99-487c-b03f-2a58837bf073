<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Telegram <PERSON><PERSON></title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        h1 {
            color: #0088cc;
            text-align: center;
            margin-bottom: 30px;
        }
        .section {
            margin-bottom: 30px;
            padding: 20px;
            border-left: 4px solid #0088cc;
            background-color: #f9f9f9;
        }
        .section h3 {
            margin-top: 0;
            color: #333;
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
            color: #333;
        }
        input, textarea, select {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 5px;
            font-size: 14px;
            box-sizing: border-box;
        }
        button {
            background-color: #0088cc;
            color: white;
            padding: 12px 24px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 14px;
            margin-right: 10px;
            margin-bottom: 10px;
        }
        button:hover {
            background-color: #006699;
        }
        button.secondary {
            background-color: #6c757d;
        }
        button.secondary:hover {
            background-color: #545b62;
        }
        button.success {
            background-color: #28a745;
        }
        button.success:hover {
            background-color: #218838;
        }
        button.danger {
            background-color: #dc3545;
        }
        button.danger:hover {
            background-color: #c82333;
        }
        .result {
            margin-top: 15px;
            padding: 15px;
            border-radius: 5px;
            white-space: pre-wrap;
            font-family: monospace;
            font-size: 12px;
            max-height: 300px;
            overflow-y: auto;
        }
        .success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .info {
            background-color: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 8px;
        }
        .status-connected { background-color: #28a745; }
        .status-disconnected { background-color: #dc3545; }
        .status-connecting { background-color: #ffc107; }
        .message-item {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 5px;
            padding: 10px;
            margin-bottom: 10px;
        }
        .message-header {
            font-weight: bold;
            color: #495057;
            margin-bottom: 5px;
        }
        .message-text {
            color: #212529;
        }
        .auto-refresh {
            display: flex;
            align-items: center;
            gap: 10px;
            margin-bottom: 15px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🤖 Telegram Bot Tester & Monitor</h1>
        
        <div class="section">
            <h3>📊 Bot Status</h3>
            <button onclick="checkBotStatus()">Check Bot Status</button>
            <button onclick="testConnection()">Test Connection</button>
            <div id="statusResult"></div>
        </div>

        <div class="section">
            <h3>📨 Send Test Message</h3>
            <div class="form-group">
                <label for="testChatId">Chat ID (your Telegram user ID):</label>
                <input type="text" id="testChatId" placeholder="e.g., 123456789">
                <small>💡 To find your chat ID, send a message to the bot first, then check 'Recent Messages' below</small>
            </div>
            <div class="form-group">
                <label for="testMessage">Message:</label>
                <textarea id="testMessage" rows="3" placeholder="Enter your test message here...">Hello! This is a test message from the bot tester.</textarea>
            </div>
            <button onclick="sendTestMessage()">Send Test Message</button>
            <div id="sendResult"></div>
        </div>

        <div class="section">
            <h3>💬 Recent Messages & Responses</h3>
            <div class="auto-refresh">
                <button onclick="getRecentMessages()">Refresh Messages</button>
                <button onclick="processMessages()" class="success">Process & Respond to Messages</button>
                <label>
                    <input type="checkbox" id="autoRefresh"> Auto-refresh every 5 seconds
                </label>
            </div>
            <div id="messagesResult"></div>
        </div>

        <div class="section">
            <h3>🔄 Integrated Bot Polling Control</h3>
            <p>Control automatic message processing (integrated with main server):</p>
            <button onclick="startPolling()" class="success">🚀 Start Bot Polling</button>
            <button onclick="stopPolling()" class="danger">🛑 Stop Bot Polling</button>
            <button onclick="checkPollingStatus()" class="secondary">📊 Check Status</button>
            <div id="pollingResult"></div>
        </div>

        <div class="section">
            <h3>🛠️ Bot Instructions</h3>
            <div class="info">
                <strong>How to test your bot:</strong><br>
                1. Make sure your bot token is configured in config.json<br>
                2. Check bot status above to ensure it's connected<br>
                3. Send a message to your bot on Telegram (search for your bot's username)<br>
                4. Click "Refresh Messages" to see incoming messages<br>
                5. Click "Process & Respond to Messages" to make the bot reply<br>
                6. Or run the bot_runner.py script for automatic responses
            </div>
        </div>
    </div>

    <script>
        let autoRefreshInterval = null;

        function checkBotStatus() {
            fetch('/api/telegram/test')
            .then(response => response.json())
            .then(data => {
                const resultDiv = document.getElementById('statusResult');
                if (data.success) {
                    const botInfo = data.data || {};
                    resultDiv.innerHTML = `
                        <div class="result success">
                            <span class="status-indicator status-connected"></span>
                            ✅ Bot is connected and working!
                            
                            Bot Info:
                            • Name: ${botInfo.first_name || 'Unknown'}
                            • Username: @${botInfo.username || 'unknown'}
                            • ID: ${botInfo.id || 'unknown'}
                            • Test Duration: ${data.test_duration_ms || 0}ms
                        </div>`;
                } else {
                    resultDiv.innerHTML = `
                        <div class="result error">
                            <span class="status-indicator status-disconnected"></span>
                            ❌ Bot connection failed: ${data.error}
                        </div>`;
                }
            })
            .catch(error => {
                document.getElementById('statusResult').innerHTML = 
                    `<div class="result error">❌ Error: ${error.message}</div>`;
            });
        }

        function testConnection() {
            fetch('/api/telegram/test')
            .then(response => response.json())
            .then(data => {
                const resultDiv = document.getElementById('statusResult');
                resultDiv.innerHTML = `<div class="result info">🔍 Connection test result:\n${JSON.stringify(data, null, 2)}</div>`;
            })
            .catch(error => {
                document.getElementById('statusResult').innerHTML = 
                    `<div class="result error">❌ Connection test failed: ${error.message}</div>`;
            });
        }

        function sendTestMessage() {
            const chatId = document.getElementById('testChatId').value;
            const message = document.getElementById('testMessage').value;

            if (!chatId || !message) {
                document.getElementById('sendResult').innerHTML = 
                    '<div class="result error">❌ Please enter both chat ID and message</div>';
                return;
            }

            fetch('/api/telegram/send-test', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    chat_id: chatId,
                    message: message
                })
            })
            .then(response => response.json())
            .then(data => {
                const resultDiv = document.getElementById('sendResult');
                if (data.success) {
                    resultDiv.innerHTML = '<div class="result success">✅ Message sent successfully!</div>';
                } else {
                    resultDiv.innerHTML = `<div class="result error">❌ Failed to send message: ${data.detail || 'Unknown error'}</div>`;
                }
            })
            .catch(error => {
                document.getElementById('sendResult').innerHTML = 
                    `<div class="result error">❌ Error: ${error.message}</div>`;
            });
        }

        function getRecentMessages() {
            fetch('/api/telegram/updates')
            .then(response => response.json())
            .then(data => {
                const resultDiv = document.getElementById('messagesResult');
                if (data.success && data.data.result) {
                    const updates = data.data.result;
                    if (updates.length === 0) {
                        resultDiv.innerHTML = '<div class="result info">📭 No recent messages found</div>';
                    } else {
                        let html = '<div class="result success"><strong>📨 Recent Messages:</strong><br><br>';
                        updates.slice(-10).forEach(update => {
                            if (update.message) {
                                const msg = update.message;
                                const userName = msg.from.first_name || 'Unknown';
                                const chatId = msg.chat.id;
                                const text = msg.text || '[Media/Other]';
                                const date = new Date(msg.date * 1000).toLocaleString();
                                
                                html += `
                                    <div class="message-item">
                                        <div class="message-header">
                                            👤 ${userName} (ID: ${chatId}) - ${date}
                                        </div>
                                        <div class="message-text">💬 ${text}</div>
                                    </div>`;
                            }
                        });
                        html += '</div>';
                        resultDiv.innerHTML = html;
                    }
                } else {
                    resultDiv.innerHTML = `<div class="result error">❌ Failed to get messages: ${data.error || 'Unknown error'}</div>`;
                }
            })
            .catch(error => {
                document.getElementById('messagesResult').innerHTML = 
                    `<div class="result error">❌ Error: ${error.message}</div>`;
            });
        }

        function processMessages() {
            fetch('/api/telegram/process-updates', {
                method: 'POST'
            })
            .then(response => response.json())
            .then(data => {
                const resultDiv = document.getElementById('messagesResult');
                if (data.success) {
                    let html = `<div class="result success">✅ ${data.message}<br><br>`;
                    
                    if (data.details && data.details.length > 0) {
                        html += '<strong>📋 Processing Details:</strong><br>';
                        data.details.forEach(detail => {
                            if (detail.user_name) {
                                html += `
                                    <div class="message-item">
                                        <div class="message-header">
                                            👤 ${detail.user_name} (${detail.chat_id})
                                        </div>
                                        <div class="message-text">
                                            📨 Received: "${detail.incoming_message}"<br>
                                            🤖 Responded: "${detail.response}"<br>
                                            ${detail.sent_successfully ? '✅ Sent successfully' : '❌ Failed to send'}
                                        </div>
                                    </div>`;
                            }
                        });
                    }
                    html += '</div>';
                    resultDiv.innerHTML = html;
                } else {
                    resultDiv.innerHTML = `<div class="result error">❌ Failed to process messages: ${data.detail || 'Unknown error'}</div>`;
                }
            })
            .catch(error => {
                document.getElementById('messagesResult').innerHTML = 
                    `<div class="result error">❌ Error: ${error.message}</div>`;
            });
        }

        function startPolling() {
            const resultDiv = document.getElementById('pollingResult');
            resultDiv.innerHTML = '<div class="result info">🚀 Starting integrated bot polling...</div>';

            fetch('/api/telegram/polling', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    action: 'start',
                    interval: 2.0
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    const botInfo = data.bot_info || {};
                    resultDiv.innerHTML = `
                        <div class="result success">
                            ✅ <strong>Bot Polling Started Successfully!</strong><br><br>
                            🤖 Bot: @${botInfo.username || 'unknown'}<br>
                            🆔 ID: ${botInfo.id || 'unknown'}<br>
                            ⏱️ Check interval: ${data.interval} seconds<br><br>

                            <strong>🎉 Your bot is now automatically responding to messages!</strong><br>
                            • Send messages to your bot on Telegram<br>
                            • Responses will be sent automatically<br>
                            • No need to manually process messages<br>
                            • Integrated with the main API server<br><br>

                            <button onclick="stopPolling()" class="danger">🛑 Stop Polling</button>
                            <button onclick="checkPollingStatus()">📊 Check Status</button>
                        </div>`;
                } else {
                    resultDiv.innerHTML = `
                        <div class="result error">
                            ❌ Failed to start bot polling: ${data.detail || data.error || 'Unknown error'}<br><br>
                            💡 Troubleshooting:<br>
                            • Check that your bot token is configured<br>
                            • Ensure the bot is not blocked<br>
                            • Verify API server is running
                        </div>`;
                }
            })
            .catch(error => {
                resultDiv.innerHTML = `
                    <div class="result error">
                        ❌ Error starting bot polling: ${error.message}<br><br>
                        💡 Make sure your API server is running on port 8000
                    </div>`;
            });
        }

        function stopPolling() {
            const resultDiv = document.getElementById('pollingResult');
            resultDiv.innerHTML = '<div class="result info">🛑 Stopping bot polling...</div>';

            fetch('/api/telegram/polling', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    action: 'stop'
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    const stats = data.stats || {};
                    resultDiv.innerHTML = `
                        <div class="result success">
                            🛑 <strong>Bot Polling Stopped Successfully!</strong><br><br>
                            📊 Session Statistics:<br>
                            • Messages processed: ${stats.messages_processed || 0}<br>
                            • Responses sent: ${stats.responses_sent || 0}<br>
                            • Errors encountered: ${stats.errors || 0}<br>
                            • Session duration: ${stats.start_time ?
                                Math.round((new Date() - new Date(stats.start_time)) / 1000 / 60) + ' minutes' :
                                'Unknown'}<br><br>

                            <button onclick="startPolling()" class="success">🚀 Start Polling Again</button>
                        </div>`;
                } else {
                    resultDiv.innerHTML = `<div class="result error">❌ Failed to stop bot polling: ${data.detail || 'Unknown error'}</div>`;
                }
            })
            .catch(error => {
                resultDiv.innerHTML = `<div class="result error">❌ Error stopping bot polling: ${error.message}</div>`;
            });
        }

        function checkPollingStatus() {
            fetch('/api/telegram/polling/status')
            .then(response => response.json())
            .then(data => {
                const resultDiv = document.getElementById('pollingResult');
                if (data.success) {
                    const polling = data.polling;
                    const stats = polling.stats || {};
                    const isRunning = polling.is_running;

                    resultDiv.innerHTML = `
                        <div class="result ${isRunning ? 'success' : 'info'}">
                            📊 <strong>Bot Polling Status</strong><br><br>

                            🔄 Status: ${isRunning ? '✅ Running' : '❌ Stopped'}<br>
                            🤖 Bot Configured: ${polling.bot_configured ? '✅ Yes' : '❌ No'}<br><br>

                            📈 <strong>Statistics:</strong><br>
                            • Messages processed: ${stats.messages_processed || 0}<br>
                            • Responses sent: ${stats.responses_sent || 0}<br>
                            • Errors: ${stats.errors || 0}<br>
                            • Started: ${stats.start_time ? new Date(stats.start_time).toLocaleString() : 'Not started'}<br>
                            • Last activity: ${stats.last_activity ? new Date(stats.last_activity).toLocaleString() : 'None'}<br><br>

                            ${isRunning ?
                                '<button onclick="stopPolling()" class="danger">🛑 Stop Polling</button>' :
                                '<button onclick="startPolling()" class="success">🚀 Start Polling</button>'
                            }
                            <button onclick="checkPollingStatus()">🔄 Refresh Status</button>
                        </div>`;
                } else {
                    resultDiv.innerHTML = `<div class="result error">❌ Failed to get polling status: ${data.error || 'Unknown error'}</div>`;
                }
            })
            .catch(error => {
                document.getElementById('pollingResult').innerHTML = `<div class="result error">❌ Error: ${error.message}</div>`;
            });
        }

        // Auto-refresh functionality
        document.getElementById('autoRefresh').addEventListener('change', function() {
            if (this.checked) {
                autoRefreshInterval = setInterval(getRecentMessages, 5000);
                console.log('Auto-refresh enabled');
            } else {
                if (autoRefreshInterval) {
                    clearInterval(autoRefreshInterval);
                    autoRefreshInterval = null;
                }
                console.log('Auto-refresh disabled');
            }
        });

        // Initial status check
        window.addEventListener('load', function() {
            checkBotStatus();
        });
    </script>
</body>
</html>
