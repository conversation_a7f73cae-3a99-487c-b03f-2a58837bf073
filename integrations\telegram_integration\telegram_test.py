"""
Comprehensive test suite for enhanced Telegram messaging integration
Tests all major functionality including error handling, rate limiting, and async operations
"""

import asyncio
import json
import time
import unittest
from unittest.mock import Mock, patch, MagicMock
from datetime import datetime
import sys
import os

# Add the integration directory to the path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from telegram_api import (
    TelegramMessaging, UnipileClient, MessageResult, ConnectionStatus,
    sanitize_message, format_html_message, RateLimitConfig
)


class TestTelegramMessaging(unittest.TestCase):
    """Test cases for TelegramMessaging class"""
    
    def setUp(self):
        """Set up test fixtures"""
        self.test_bot_token = "123456789:ABCdefGHIjklMNOpqrsTUVwxyz"
        self.test_unipile_key = "test_unipile_key"
        
        # Mock the config loading to avoid file dependencies
        with patch('telegram_api.get_config_path'), \
             patch('builtins.open'), \
             patch('json.load') as mock_json_load:
            
            mock_json_load.return_value = {
                "bot_token": self.test_bot_token,
                "rate_limit": {
                    "messages_per_second": 30,
                    "messages_per_minute": 20,
                    "messages_per_chat_per_second": 1
                },
                "settings": {
                    "auto_retry": True,
                    "max_retries": 3,
                    "retry_delay": 1,
                    "log_level": "INFO",
                    "max_message_length": 4096
                },
                "retry_settings": {
                    "max_retries": 3,
                    "retry_delay": 1.0
                }
            }
            
            self.telegram = TelegramMessaging(
                bot_token=self.test_bot_token,
                unipile_api_key=self.test_unipile_key
            )

    def test_initialization(self):
        """Test proper initialization of TelegramMessaging"""
        self.assertEqual(self.telegram.bot_token, self.test_bot_token)
        self.assertEqual(self.telegram.unipile_api_key, self.test_unipile_key)
        self.assertIsInstance(self.telegram.rate_limit, RateLimitConfig)
        self.assertIsInstance(self.telegram.connection_status, ConnectionStatus)

    def test_message_sanitization(self):
        """Test message sanitization functionality"""
        # Test normal message
        clean_msg = sanitize_message("Hello, world!")
        self.assertEqual(clean_msg, "Hello, world!")
        
        # Test message with control characters
        dirty_msg = "Hello\x00\x08world\x1F!"
        clean_msg = sanitize_message(dirty_msg)
        self.assertEqual(clean_msg, "Helloworld!")
        
        # Test empty message
        self.assertEqual(sanitize_message(""), "")
        self.assertEqual(sanitize_message(None), "")
        
        # Test long message truncation
        long_msg = "A" * 5000
        clean_msg = sanitize_message(long_msg, max_length=100)
        self.assertEqual(len(clean_msg), 100)
        self.assertTrue(clean_msg.endswith("..."))

    def test_html_formatting(self):
        """Test HTML message formatting"""
        text = "Hello & <world>"
        
        # Test basic escaping
        formatted = format_html_message(text)
        self.assertEqual(formatted, "Hello &amp; &lt;world&gt;")
        
        # Test bold formatting
        formatted = format_html_message(text, bold=True)
        self.assertEqual(formatted, "<b>Hello &amp; &lt;world&gt;</b>")
        
        # Test multiple formatting
        formatted = format_html_message(text, bold=True, italic=True)
        self.assertEqual(formatted, "<i><b>Hello &amp; &lt;world&gt;</b></i>")

    @patch('telegram_api.requests.get')
    def test_connection_test_success(self, mock_get):
        """Test successful connection test"""
        # Mock successful API response
        mock_response = Mock()
        mock_response.status_code = 200
        mock_response.json.return_value = {
            "ok": True,
            "result": {
                "id": 123456789,
                "is_bot": True,
                "first_name": "TestBot",
                "username": "test_bot",
                "can_join_groups": True,
                "can_read_all_group_messages": False,
                "supports_inline_queries": False
            }
        }
        mock_get.return_value = mock_response
        
        result = self.telegram.test_connection()
        
        self.assertTrue(result["success"])
        self.assertEqual(result["status"], ConnectionStatus.CONNECTED.value)
        self.assertIn("bot_info", result)
        self.assertIn("health_info", result)
        self.assertIn("test_duration_ms", result)

    @patch('telegram_api.requests.get')
    def test_connection_test_failure(self, mock_get):
        """Test failed connection test"""
        # Mock failed API response
        mock_response = Mock()
        mock_response.status_code = 401
        mock_response.json.return_value = {
            "ok": False,
            "error_code": 401,
            "description": "Unauthorized"
        }
        mock_get.return_value = mock_response
        
        result = self.telegram.test_connection()
        
        self.assertFalse(result["success"])
        self.assertEqual(result["status"], ConnectionStatus.ERROR.value)
        self.assertIn("error", result)
        self.assertEqual(result["error_code"], 401)

    @patch('telegram_api.requests.post')
    def test_send_message_success(self, mock_post):
        """Test successful message sending"""
        # Mock successful API response
        mock_response = Mock()
        mock_response.status_code = 200
        mock_response.json.return_value = {
            "ok": True,
            "result": {
                "message_id": 123,
                "date": 1234567890,
                "chat": {"id": 987654321, "type": "private"},
                "text": "Test message"
            }
        }
        mock_post.return_value = mock_response
        
        # Mock Unipile failure to test bot API fallback
        with patch.object(self.telegram, '_send_via_unipile') as mock_unipile:
            mock_unipile.return_value = {"error": "Unipile not available"}
            
            # Mock successful connection status
            self.telegram.connection_status = ConnectionStatus.CONNECTED
            
            result = self.telegram.send_message("123456789", "Test message")
            
            self.assertIsInstance(result, MessageResult)
            self.assertTrue(result.success)
            self.assertEqual(result.message_id, "123")
            self.assertEqual(result.method, "bot_api")

    def test_send_message_validation(self):
        """Test message validation in send_message"""
        # Test empty message
        result = self.telegram.send_message("123456789", "")
        self.assertIsInstance(result, MessageResult)
        self.assertFalse(result.success)
        self.assertIn("empty after sanitization", result.error)
        
        # Test None message
        result = self.telegram.send_message("123456789", None)
        self.assertFalse(result.success)
        self.assertIn("required and must be a string", result.error)
        
        # Test empty user_id
        result = self.telegram.send_message("", "Test message")
        self.assertFalse(result.success)
        self.assertIn("User ID is required", result.error)

    def test_rate_limiting(self):
        """Test rate limiting functionality"""
        # Test that rate limiting doesn't block immediately
        start_time = time.time()
        self.telegram._enhanced_rate_limit("test_chat")
        first_call_time = time.time() - start_time
        
        # Should be very fast for first call
        self.assertLess(first_call_time, 0.1)
        
        # Test per-chat rate limiting
        start_time = time.time()
        self.telegram._enhanced_rate_limit("test_chat")
        second_call_time = time.time() - start_time
        
        # Should have some delay for same chat
        self.assertGreater(second_call_time, 0.5)  # Based on default 1 msg/sec per chat

    @patch('telegram_api.requests.post')
    def test_bulk_messaging(self, mock_post):
        """Test bulk message functionality"""
        # Mock successful API responses
        mock_response = Mock()
        mock_response.status_code = 200
        mock_response.json.return_value = {
            "ok": True,
            "result": {"message_id": 123}
        }
        mock_post.return_value = mock_response
        
        # Mock Unipile failure
        with patch.object(self.telegram, '_send_via_unipile') as mock_unipile:
            mock_unipile.return_value = {"error": "Unipile not available"}
            self.telegram.connection_status = ConnectionStatus.CONNECTED
            
            recipients = ["123", "456", "789"]
            results = self.telegram.send_bulk_messages(recipients, "Bulk test", delay=0.1)
            
            self.assertEqual(len(results), 3)
            for result in results:
                self.assertIn("chat_id", result)
                self.assertIn("result", result)
                self.assertIn("timestamp", result)
                self.assertIn("sequence", result)

    def test_connection_health(self):
        """Test connection health reporting"""
        health = self.telegram.get_connection_health()
        
        self.assertIn("status", health)
        self.assertIn("bot_configured", health)
        self.assertIn("rate_limit_config", health)
        self.assertIn("message_stats", health)
        self.assertIn("timestamp", health)
        
        # Bot should be configured with our test token
        self.assertTrue(health["bot_configured"])


class TestUnipileClient(unittest.TestCase):
    """Test cases for UnipileClient class"""
    
    def setUp(self):
        self.client = UnipileClient("test_api_key", max_retries=2, retry_delay=0.1)

    @patch('telegram_api.requests.get')
    def test_successful_request(self, mock_get):
        """Test successful Unipile API request"""
        mock_response = Mock()
        mock_response.status_code = 200
        mock_response.content = b'{"success": true, "data": "test"}'
        mock_response.json.return_value = {"success": True, "data": "test"}
        mock_get.return_value = mock_response
        
        result = self.client.make_request("GET", "test-endpoint")
        
        self.assertEqual(result["success"], True)
        self.assertEqual(result["data"], "test")

    @patch('telegram_api.requests.get')
    def test_retry_logic(self, mock_get):
        """Test retry logic on failures"""
        # First call fails, second succeeds
        mock_response_fail = Mock()
        mock_response_fail.status_code = 500
        mock_response_fail.raise_for_status.side_effect = Exception("Server Error")
        
        mock_response_success = Mock()
        mock_response_success.status_code = 200
        mock_response_success.content = b'{"success": true}'
        mock_response_success.json.return_value = {"success": True}
        
        mock_get.side_effect = [mock_response_fail, mock_response_success]
        
        result = self.client.make_request("GET", "test-endpoint")
        
        self.assertEqual(result["success"], True)
        self.assertEqual(mock_get.call_count, 2)


if __name__ == "__main__":
    # Run the tests
    unittest.main(verbosity=2)
