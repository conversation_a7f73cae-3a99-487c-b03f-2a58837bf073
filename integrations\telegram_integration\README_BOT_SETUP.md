# 🤖 Telegram Bot Setup & Message Response Guide

## Why Your Bot Can't See Messages (And How to Fix It)

Your Telegram bot can **send** messages but can't **see** or **respond** to incoming messages because:

1. **No Message Polling**: Bots need to actively check for new messages
2. **No Response Logic**: Even if messages are received, there's no code to respond
3. **No Background Process**: The bot needs to run continuously to monitor messages

## 🚀 Quick Fix - Make Your Bot Respond to Messages

### Option 1: Run the Bot Runner (Recommended)

1. **Open a terminal** in the `integrations/telegram_integration` folder
2. **Run the bot runner**:
   ```bash
   python bot_runner.py
   ```
3. **Send a message** to your bot on Telegram
4. **Watch the terminal** - you'll see the bot processing and responding!

### Option 2: Use the Web Interface

1. **Open your browser** and go to: `http://localhost:8000/telegram/bot_tester.html`
2. **Check bot status** to ensure it's connected
3. **Send a message** to your bot on Telegram
4. **Click "Refresh Messages"** to see incoming messages
5. **Click "Process & Respond to Messages"** to make the bot reply

### Option 3: Manual API Testing

Use the API endpoints to test:
- `GET /api/telegram/updates` - See incoming messages
- `POST /api/telegram/process-updates` - Process and respond to messages

## 🔧 How It Works

### Message Flow:
```
User sends message → Telegram servers → Your bot checks for updates → Bot processes message → Bot sends response
```

### Bot Response Examples:

When users send:
- `"hello"` or `"/start"` → Bot replies with welcome message
- `"help"` → Bot shows available commands
- `"status"` → Bot shows current status
- `"echo hello world"` → Bot replies: "🔄 You said: hello world"
- `"time"` → Bot shows current time
- Any question with `"?"` → Bot gives a helpful response

## 📋 Step-by-Step Setup

### 1. Verify Bot Configuration
```bash
# Check if your bot token is valid
curl -X GET "http://localhost:8000/api/telegram/test"
```

### 2. Test Message Sending
```bash
# Send a test message (replace CHAT_ID with your Telegram user ID)
curl -X POST "http://localhost:8000/api/telegram/send-test" \
  -H "Content-Type: application/json" \
  -d '{"chat_id": "YOUR_CHAT_ID", "message": "Test message"}'
```

### 3. Check for Incoming Messages
```bash
# See if there are any messages waiting
curl -X GET "http://localhost:8000/api/telegram/updates"
```

### 4. Process Messages and Respond
```bash
# Make the bot respond to pending messages
curl -X POST "http://localhost:8000/api/telegram/process-updates"
```

## 🤖 Bot Commands Your Users Can Try

Once your bot is running, users can send:

- **`/start`** - Start conversation with the bot
- **`help`** - Show help menu with available commands
- **`status`** - Check bot status and statistics
- **`echo [message]`** - Bot will echo back the message
- **`time`** - Get current time
- **`info`** - Get bot information
- **Any question** - Bot will try to give a helpful response

## 🔍 Troubleshooting

### Bot Not Responding?

1. **Check bot status**:
   ```bash
   python -c "
   from telegram_api import TelegramMessaging
   bot = TelegramMessaging()
   print('Status:', bot.test_connection())
   "
   ```

2. **Check for messages manually**:
   ```bash
   python -c "
   from telegram_api import TelegramMessaging
   bot = TelegramMessaging()
   updates = bot.get_updates()
   print('Updates:', updates)
   "
   ```

3. **Test response generation**:
   ```bash
   python -c "
   from telegram_api import TelegramMessaging
   bot = TelegramMessaging()
   response = bot._generate_response('hello', 'TestUser', {})
   print('Response:', response)
   "
   ```

### Common Issues:

- **"Bot token not configured"** → Check `config.json` has valid bot token
- **"Connection failed"** → Verify bot token with @BotFather
- **"No updates"** → Send a message to your bot first
- **"Failed to send response"** → Check bot permissions and token

## 🚀 Advanced Usage

### Run Bot in Background
```bash
# Linux/Mac
nohup python bot_runner.py > bot.log 2>&1 &

# Windows (PowerShell)
Start-Process python -ArgumentList "bot_runner.py" -WindowStyle Hidden
```

### Custom Response Logic
Edit the `_generate_response()` method in `telegram_api.py` to customize how your bot responds to different messages.

### Integration with Your App
Use the API endpoints in your application:
```python
import requests

# Check for new messages
response = requests.get("http://localhost:8000/api/telegram/updates")
updates = response.json()

# Process and respond
response = requests.post("http://localhost:8000/api/telegram/process-updates")
result = response.json()
```

## 📊 Monitoring Your Bot

### Real-time Monitoring
- Use the web interface at `/telegram/bot_tester.html`
- Enable auto-refresh to see messages in real-time
- Monitor bot statistics and response rates

### Logs
- Bot runner shows detailed logs in the terminal
- Check for errors and response times
- Monitor message processing statistics

## 🎉 Success!

Once set up correctly, your bot will:
- ✅ Automatically see incoming messages
- ✅ Generate appropriate responses
- ✅ Reply to users in real-time
- ✅ Handle multiple conversations
- ✅ Provide helpful information and commands

Your bot is now ready to have conversations with users! 🎊
