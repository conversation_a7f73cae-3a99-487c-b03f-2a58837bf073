"""
Manual Bot Testing - Process messages and respond manually
Use this when the API endpoint isn't working
"""

import sys
import os

# Add current directory to path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from telegram_api import TelegramMessaging

def manual_process_messages():
    """Manually process and respond to messages"""
    print("🤖 Manual Message Processing")
    print("=" * 40)
    
    try:
        # Initialize bot
        bot = TelegramMessaging()
        print(f"✅ Bot initialized: @{bot.bot_info.get('username') if bot.bot_info else 'Unknown'}")
        
        # Get updates
        print("\n📨 Checking for new messages...")
        updates_result = bot.get_updates()
        
        if not updates_result.get("ok"):
            print(f"❌ Failed to get updates: {updates_result}")
            return
        
        updates = updates_result.get("result", [])
        
        if not updates:
            print("📭 No new messages found")
            print("💡 Send a message to your bot and try again!")
            return
        
        print(f"✅ Found {len(updates)} message(s)")
        
        # Process each message
        for i, update in enumerate(updates, 1):
            if "message" in update:
                message = update["message"]
                chat_id = message["chat"]["id"]
                user_name = message.get("from", {}).get("first_name", "User")
                text = message.get("text", "")
                
                print(f"\n📨 Message {i}:")
                print(f"   👤 From: {user_name}")
                print(f"   🆔 Chat ID: {chat_id}")
                print(f"   💬 Text: {text}")
                
                # Generate response
                response = bot._generate_response(text, user_name, message)
                if response:
                    print(f"   🤖 Bot will respond: {response[:100]}...")
                    
                    # Send response
                    result = bot.send_message(chat_id, response)
                    if hasattr(result, 'success') and result.success:
                        print(f"   ✅ Response sent successfully!")
                    else:
                        error = result.error if hasattr(result, 'error') else str(result)
                        print(f"   ❌ Failed to send response: {error}")
                else:
                    print(f"   ⚠️  No response generated")
        
        # Mark messages as processed
        if updates:
            last_update_id = updates[-1]["update_id"]
            bot.get_updates(offset=last_update_id + 1, limit=1)
            print(f"\n✅ Marked {len(updates)} messages as processed")
        
        print(f"\n🎉 Processing complete!")
        
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()

def send_test_message():
    """Send a test message to a specific chat ID"""
    print("\n📤 Send Test Message")
    print("-" * 30)
    
    # Use the chat IDs we found
    available_chat_ids = ["5001883382", "878811084"]
    
    print("Available chat IDs from recent messages:")
    for i, chat_id in enumerate(available_chat_ids, 1):
        print(f"   {i}. {chat_id}")
    
    try:
        choice = input(f"\nEnter choice (1-{len(available_chat_ids)}) or custom chat ID: ").strip()
        
        if choice.isdigit() and 1 <= int(choice) <= len(available_chat_ids):
            chat_id = available_chat_ids[int(choice) - 1]
        else:
            chat_id = choice
        
        message = input("Enter message to send: ").strip()
        if not message:
            message = "🧪 Test message from manual testing script!"
        
        # Send message
        bot = TelegramMessaging()
        result = bot.send_message(chat_id, message)
        
        if hasattr(result, 'success') and result.success:
            print(f"✅ Message sent successfully to {chat_id}!")
        else:
            error = result.error if hasattr(result, 'error') else str(result)
            print(f"❌ Failed to send message: {error}")
            
    except KeyboardInterrupt:
        print("\n👋 Cancelled")
    except Exception as e:
        print(f"❌ Error: {e}")

def main():
    """Main menu"""
    while True:
        print("\n🤖 Telegram Bot Manual Testing")
        print("=" * 40)
        print("1. 📨 Process incoming messages and respond")
        print("2. 📤 Send test message")
        print("3. 🔍 Show recent messages (no response)")
        print("4. 🚪 Exit")
        
        try:
            choice = input("\nEnter your choice (1-4): ").strip()
            
            if choice == "1":
                manual_process_messages()
            elif choice == "2":
                send_test_message()
            elif choice == "3":
                bot = TelegramMessaging()
                updates = bot.get_updates()
                if updates.get("ok"):
                    messages = updates.get("result", [])
                    if messages:
                        print(f"\n📨 Recent {len(messages)} message(s):")
                        for msg in messages[-5:]:
                            if "message" in msg:
                                m = msg["message"]
                                user = m.get("from", {}).get("first_name", "Unknown")
                                text = m.get("text", "[Media]")
                                chat_id = m["chat"]["id"]
                                print(f"   👤 {user} ({chat_id}): {text}")
                    else:
                        print("📭 No recent messages")
                else:
                    print("❌ Failed to get messages")
            elif choice == "4":
                print("👋 Goodbye!")
                break
            else:
                print("❌ Invalid choice")
                
        except KeyboardInterrupt:
            print("\n👋 Goodbye!")
            break
        except Exception as e:
            print(f"❌ Error: {e}")

if __name__ == "__main__":
    main()
