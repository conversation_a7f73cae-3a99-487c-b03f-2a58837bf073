"""
Telegram Bot Runner - Makes your bot respond to messages automatically
Run this script to start your bot and make it respond to incoming messages
"""

import sys
import os
import time
import signal
from datetime import datetime

# Add current directory to path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from telegram_api import TelegramMessaging, ConnectionStatus

class TelegramBotRunner:
    """Bot runner that handles message polling and responses"""
    
    def __init__(self):
        self.telegram = None
        self.running = False
        self.stats = {
            "start_time": None,
            "messages_processed": 0,
            "responses_sent": 0,
            "errors": 0
        }
    
    def setup_bot(self):
        """Initialize and test bot connection"""
        print("🤖 Setting up Telegram Bot...")
        
        try:
            # Initialize bot
            self.telegram = TelegramMessaging()
            
            # Test connection
            print("🔍 Testing bot connection...")
            connection_test = self.telegram.test_connection()
            
            if connection_test.get("success"):
                bot_info = connection_test.get("bot_info", {})
                print(f"✅ Bot connected successfully!")
                print(f"   🤖 Bot: @{bot_info.get('username', 'unknown')}")
                print(f"   🆔 ID: {bot_info.get('id', 'unknown')}")
                print(f"   📝 Name: {bot_info.get('first_name', 'Unknown')}")
                return True
            else:
                print(f"❌ Bot connection failed: {connection_test.get('error')}")
                print("\n🔧 Troubleshooting:")
                print("   1. Check your bot token in config.json")
                print("   2. Make sure the token is valid (get it from @BotFather)")
                print("   3. Ensure the bot is not blocked")
                return False
                
        except Exception as e:
            print(f"❌ Error setting up bot: {e}")
            return False
    
    def check_for_messages(self):
        """Check for new messages and respond"""
        try:
            # Get updates
            updates_result = self.telegram.get_updates()
            
            if updates_result.get("ok") and updates_result.get("result"):
                updates = updates_result["result"]
                
                if updates:
                    print(f"\n📨 Found {len(updates)} new message(s)")
                    
                    # Process each update
                    for update in updates:
                        if "message" in update:
                            message = update["message"]
                            chat_id = message["chat"]["id"]
                            user_name = message.get("from", {}).get("first_name", "User")
                            text = message.get("text", "")
                            
                            print(f"   💬 From {user_name}: {text}")
                            
                            # Generate and send response
                            response = self.telegram._generate_response(text, user_name, message)
                            if response:
                                result = self.telegram.send_message(chat_id, response)
                                if hasattr(result, 'success') and result.success:
                                    print(f"   ✅ Replied: {response[:50]}...")
                                    self.stats["responses_sent"] += 1
                                else:
                                    print(f"   ❌ Failed to send reply: {result.error if hasattr(result, 'error') else 'Unknown error'}")
                                    self.stats["errors"] += 1
                            
                            self.stats["messages_processed"] += 1
                    
                    # Mark messages as processed (this is a simple approach)
                    # In production, you'd want to store the last update_id
                    if updates:
                        last_update_id = updates[-1]["update_id"]
                        # Get updates with offset to mark as processed
                        self.telegram.get_updates(offset=last_update_id + 1, limit=1)
                
            return True
            
        except Exception as e:
            print(f"❌ Error checking messages: {e}")
            self.stats["errors"] += 1
            return False
    
    def print_stats(self):
        """Print current statistics"""
        if self.stats["start_time"]:
            runtime = datetime.now() - self.stats["start_time"]
            print(f"\n📊 Bot Statistics:")
            print(f"   ⏱️  Runtime: {runtime}")
            print(f"   📨 Messages processed: {self.stats['messages_processed']}")
            print(f"   ✅ Responses sent: {self.stats['responses_sent']}")
            print(f"   ❌ Errors: {self.stats['errors']}")
    
    def signal_handler(self, signum, frame):
        """Handle shutdown signals"""
        print(f"\n🛑 Received signal {signum}, shutting down...")
        self.running = False
    
    def run(self, check_interval=2.0):
        """Run the bot with message checking"""
        print("\n🚀 Starting Telegram Bot Runner")
        print("=" * 50)
        
        # Setup signal handlers
        signal.signal(signal.SIGINT, self.signal_handler)
        signal.signal(signal.SIGTERM, self.signal_handler)
        
        # Setup bot
        if not self.setup_bot():
            return False
        
        print(f"\n🔄 Starting message polling (checking every {check_interval} seconds)")
        print("💡 Send a message to your bot to test it!")
        print("🛑 Press Ctrl+C to stop")
        
        self.running = True
        self.stats["start_time"] = datetime.now()
        
        try:
            while self.running:
                # Check for messages
                self.check_for_messages()
                
                # Print stats every 30 seconds
                if self.stats["messages_processed"] > 0 and self.stats["messages_processed"] % 10 == 0:
                    self.print_stats()
                
                # Wait before next check
                time.sleep(check_interval)
                
        except KeyboardInterrupt:
            print("\n🛑 Stopped by user")
        except Exception as e:
            print(f"\n❌ Unexpected error: {e}")
        finally:
            self.running = False
            self.print_stats()
            print("\n👋 Bot runner stopped")
        
        return True

def main():
    """Main function"""
    print("🤖 Telegram Bot Message Responder")
    print("=" * 40)
    print("This script will make your bot respond to incoming messages automatically.")
    print()
    
    # Check if we have a valid configuration
    config_path = os.path.join(os.path.dirname(__file__), "config.json")
    if not os.path.exists(config_path):
        print("❌ Config file not found!")
        print(f"   Expected: {config_path}")
        print("   Please make sure config.json exists with your bot token.")
        return False
    
    # Create and run bot
    runner = TelegramBotRunner()
    success = runner.run(check_interval=2.0)  # Check every 2 seconds
    
    return success

if __name__ == "__main__":
    try:
        success = main()
        sys.exit(0 if success else 1)
    except Exception as e:
        print(f"❌ Fatal error: {e}")
        sys.exit(1)
