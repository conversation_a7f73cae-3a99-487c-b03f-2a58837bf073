<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Telegram Bot Setup</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #0088cc;
            text-align: center;
            margin-bottom: 30px;
        }
        .step {
            margin-bottom: 25px;
            padding: 20px;
            border-left: 4px solid #0088cc;
            background-color: #f9f9f9;
        }
        .step h3 {
            margin-top: 0;
            color: #333;
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
            color: #333;
        }
        input[type="text"], input[type="password"], textarea {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 5px;
            font-size: 14px;
            box-sizing: border-box;
        }
        button {
            background-color: #0088cc;
            color: white;
            padding: 12px 24px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 14px;
            margin-right: 10px;
            margin-bottom: 10px;
        }
        button:hover {
            background-color: #006699;
        }
        .success {
            background-color: #d4edda;
            color: #155724;
            padding: 15px;
            border-radius: 5px;
            margin-top: 15px;
        }
        .error {
            background-color: #f8d7da;
            color: #721c24;
            padding: 15px;
            border-radius: 5px;
            margin-top: 15px;
        }
        .code {
            background-color: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            font-family: 'Courier New', monospace;
            margin: 10px 0;
            border: 1px solid #e9ecef;
        }
        .info {
            background-color: #d1ecf1;
            color: #0c5460;
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 20px;
        }
        ul {
            margin: 10px 0;
            padding-left: 20px;
        }
        a {
            color: #0088cc;
            text-decoration: none;
        }
        a:hover {
            text-decoration: underline;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🤖 Telegram Bot Setup</h1>
        
        <div class="info">
            <strong>ℹ️ Info:</strong> Telegram bots are easy to set up and free to use. 
            You'll need to create a bot through BotFather and get your bot token.
        </div>

        <div class="step">
            <h3>Step 1: Create Your Telegram Bot</h3>
            <p>1. Open Telegram and search for <strong>@BotFather</strong></p>
            <p>2. Start a chat with BotFather and send <code>/newbot</code></p>
            <p>3. Follow the instructions to choose a name and username for your bot</p>
            <p>4. BotFather will give you a bot token - copy this token</p>
            <p>5. (Optional) Use <code>/setdescription</code> and <code>/setabouttext</code> to customize your bot</p>
        </div>

        <div class="step">
            <h3>🚀 Step 2: Connect via Unipile (Recommended)</h3>
            <div class="info">
                <strong>Unipile provides unified access to Telegram with simplified authentication and appears in your Unipile dashboard.</strong>
            </div>

            <div class="form-group">
                <label for="unipileApiKey">Unipile API Key:</label>
                <input type="password" id="unipileApiKey" name="unipileApiKey"
                       value="K0Py2YdG.SazddZm5laRo0Bk9kZ0cKnNt8PLt/AJj15NEGsM7lrk="
                       placeholder="Your Unipile API key">
            </div>

            <button type="button" onclick="connectUnipile()">Connect via Unipile</button>
            <button type="button" onclick="checkUnipileStatus()">Check Connection Status</button>

            <div id="unipileResult"></div>
            <div id="connectionStatus"></div>
        </div>

        <div class="step">
            <h3>🤖 Step 3: Alternative - Bot Token Method</h3>
            <div class="info">
                <strong>Use this method if you prefer direct bot API access (won't appear in Unipile dashboard).</strong>
            </div>

            <form id="telegramConfigForm">
                <div class="form-group">
                    <label for="botToken">Bot Token:</label>
                    <input type="password" id="botToken" name="botToken"
                           placeholder="1234567890:ABCdefGHIjklMNOpqrsTUVwxyz">
                </div>

                <button type="button" onclick="saveConfig()">Save Configuration</button>
                <button type="button" onclick="testConnection()">Test Connection</button>
            </form>

            <div id="configResult"></div>
        </div>

        <div class="step">
            <h3>Step 4: Bot Information</h3>
            <p>Once configured, you can get information about your bot:</p>
            <button type="button" onclick="getBotInfo()">Get Bot Info</button>
            <div id="botInfoResult"></div>
        </div>



        <div class="step">
            <h3>Step 5: Test Your Bot</h3>

            <!-- Single Message -->
            <div style="margin-bottom: 30px; padding: 20px; border: 1px solid #ddd; border-radius: 10px;">
                <h4>📱 Single Message</h4>
                <div class="form-group">
                    <label for="testChatId">Chat ID or Username:</label>
                    <input type="text" id="testChatId" placeholder="@username or 123456789">
                    <small style="color: #6c757d; display: block; margin-top: 5px;">
                        💡 <strong>Examples:</strong>
                        <code>@john_doe</code> • <code>john_doe</code> • <code>123456789</code> • <code>@channel_name</code>
                    </small>
                </div>
                <div class="form-group">
                    <label for="testMessage">Test Message:</label>
                    <textarea id="testMessage" rows="3" placeholder="Hello! This is a test message from your Telegram bot."></textarea>
                </div>
                <button type="button" onclick="sendTestMessage()">Send Test Message</button>
                <div id="testResult"></div>
            </div>

            <!-- Bulk Messaging -->
            <div style="padding: 20px; border: 1px solid #ddd; border-radius: 10px;">
                <h4>📢 Bulk Messaging</h4>
                <div class="form-group">
                    <label for="bulkChatIds">Chat IDs or Usernames (one per line):</label>
                    <textarea id="bulkChatIds" rows="4" placeholder="@username1&#10;@username2&#10;123456789"></textarea>
                </div>
                <div class="form-group">
                    <label for="bulkMessage">Bulk Message:</label>
                    <textarea id="bulkMessage" rows="3" placeholder="Hello! This is a bulk message from your Telegram bot."></textarea>
                </div>
                <div class="form-group">
                    <label for="bulkDelay">Delay between messages (seconds):</label>
                    <input type="number" id="bulkDelay" value="1" min="0.5" max="10" step="0.5">
                </div>
                <button type="button" onclick="sendBulkMessages()">Send Bulk Messages</button>
                <div id="bulkResult"></div>
            </div>
        </div>

        <div class="step">
            <h3>Step 6: Getting Chat IDs & Using Usernames</h3>
            <div class="info" style="margin-bottom: 15px;">
                <strong>💡 You can now use both Chat IDs and Usernames!</strong><br>
                No need to find numeric chat IDs - usernames work too!
            </div>

            <h4>🎯 Two Ways to Send Messages:</h4>
            <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px; margin-bottom: 20px;">
                <div style="padding: 15px; border: 1px solid #28a745; border-radius: 8px; background-color: #f8fff8;">
                    <h5 style="color: #28a745; margin-top: 0;">✅ Using Usernames (Easy!)</h5>
                    <ul style="margin: 0;">
                        <li><code>@username</code> - User's username</li>
                        <li><code>username</code> - Auto-adds @ symbol</li>
                        <li><code>@channelname</code> - Public channels</li>
                        <li><code>@groupname</code> - Public groups</li>
                    </ul>
                    <small style="color: #6c757d;">💡 Works if the user has a public username</small>
                </div>

                <div style="padding: 15px; border: 1px solid #17a2b8; border-radius: 8px; background-color: #f8fcff;">
                    <h5 style="color: #17a2b8; margin-top: 0;">📊 Using Chat IDs (Traditional)</h5>
                    <ul style="margin: 0;">
                        <li><code>123456789</code> - User's numeric ID</li>
                        <li><code>-1001234567890</code> - Group/channel ID</li>
                        <li>Get from bot messages below</li>
                    </ul>
                    <small style="color: #6c757d;">💡 Always works, but harder to remember</small>
                </div>
            </div>

            <h4>📨 Find Chat IDs from Recent Messages:</h4>
            <p>If you need numeric chat IDs, check recent messages sent to your bot:</p>
            <button type="button" onclick="getUpdates()">Get Recent Updates</button>
            <div id="updatesResult"></div>
        </div>

        <!-- <div class="step">
            <h3>🤖 Step 7: Bot Response System (NEW!)</h3>
            <div class="info">
                <strong>🎉 Your bot can now automatically respond to incoming messages!</strong><br>
                Users can send commands and your bot will reply intelligently.
            </div> -->

            <!-- <div class="form-group">
                <h4>🎯 Commands Your Bot Understands:</h4>
                <ul>
                    <li><code>/start</code> or <code>hello</code> - Welcome message with instructions</li>
                    <li><code>help</code> - Show available commands and features</li>
                    <li><code>status</code> - Bot health and statistics</li>
                    <li><code>echo [message]</code> - Bot repeats back the message</li>
                    <li><code>time</code> - Current date and time</li>
                    <li><code>info</code> - Bot information and capabilities</li>
                    <li>Questions with <code>?</code> - Helpful responses</li>
                    <li><code>thank you</code> - Polite acknowledgment</li>
                    <li><code>goodbye</code> - Farewell message</li>
                    <li>Any other message - Friendly response with guidance</li>
                </ul>
            </div>

            <div class="form-group">
                <h4>🔄 Process Messages & Respond:</h4>
                <button type="button" onclick="processMessages()" style="background-color: #28a745;">🤖 Process & Respond to Messages</button>
                <button type="button" onclick="getUpdates()" style="background-color: #17a2b8;">📨 Refresh Messages</button>
                <p><small>💡 Send a message to your bot on Telegram, then click "Process & Respond" to see the bot reply!</small></p>
            </div>

            <div class="form-group">
                <h4>🚀 Automatic Response Options:</h4>
                <button type="button" onclick="startBotRunner()" style="background-color: #28a745;">🏃 Start Bot Runner</button>
                <button type="button" onclick="openBotTester()" style="background-color: #007bff;">🧪 Open Bot Tester</button>
                <p><small>Bot Runner: Automatically responds to all messages in real-time</small></p>
                <p><small>Bot Tester: Advanced interface for testing and monitoring</small></p>
            </div>

            <div id="responseResult"></div>
        </div>
    </div> -->

    <script>
        function saveConfig() {
            const config = {
                bot_token: document.getElementById('botToken').value
            };
            
            fetch('/api/telegram/config', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(config)
            })
            .then(response => response.json())
            .then(data => {
                const resultDiv = document.getElementById('configResult');
                if (data.success) {
                    resultDiv.innerHTML = '<div class="success">✅ Configuration saved successfully!</div>';
                } else {
                    resultDiv.innerHTML = '<div class="error">❌ Error saving configuration: ' + data.error + '</div>';
                }
            })
            .catch(error => {
                document.getElementById('configResult').innerHTML = 
                    '<div class="error">❌ Error: ' + error.message + '</div>';
            });
        }
        
        function testConnection() {
            fetch('/api/telegram/test')
            .then(response => response.json())
            .then(data => {
                const resultDiv = document.getElementById('configResult');
                if (data.success) {
                    resultDiv.innerHTML = '<div class="success">✅ Connection test successful!</div>';
                } else {
                    resultDiv.innerHTML = '<div class="error">❌ Connection test failed: ' + data.error + '</div>';
                }
            })
            .catch(error => {
                document.getElementById('configResult').innerHTML = 
                    '<div class="error">❌ Test failed: ' + error.message + '</div>';
            });
        }
        
        function getBotInfo() {
            fetch('/api/telegram/bot-info')
            .then(response => response.json())
            .then(data => {
                const resultDiv = document.getElementById('botInfoResult');
                if (data.success) {
                    const info = data.data;
                    resultDiv.innerHTML = `
                        <div class="success">
                            <strong>Bot Information:</strong><br>
                            Name: ${info.first_name}<br>
                            Username: @${info.username}<br>
                            ID: ${info.id}<br>
                            Can Join Groups: ${info.can_join_groups ? 'Yes' : 'No'}<br>
                            Can Read All Group Messages: ${info.can_read_all_group_messages ? 'Yes' : 'No'}
                        </div>
                    `;
                } else {
                    resultDiv.innerHTML = '<div class="error">❌ Failed to get bot info: ' + data.error + '</div>';
                }
            })
            .catch(error => {
                document.getElementById('botInfoResult').innerHTML = 
                    '<div class="error">❌ Error: ' + error.message + '</div>';
            });
        }

        function connectUnipile() {
            const apiKey = document.getElementById('unipileApiKey').value;
            if (!apiKey) {
                document.getElementById('unipileResult').innerHTML =
                    '<div class="error">❌ Please enter your Unipile API key</div>';
                return;
            }

            fetch('/api/telegram/unipile/connect', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    api_key: apiKey
                })
            })
            .then(response => response.json())
            .then(data => {
                const resultDiv = document.getElementById('unipileResult');
                if (data.success) {
                    resultDiv.innerHTML = '<div class="success">✅ Unipile API connected successfully!</div>';
                    checkUnipileStatus(); // Refresh status
                } else {
                    resultDiv.innerHTML = '<div class="error">❌ Failed to connect: ' + data.error + '</div>';
                }
            })
            .catch(error => {
                document.getElementById('unipileResult').innerHTML =
                    '<div class="error">❌ Error: ' + error.message + '</div>';
            });
        }

        function checkUnipileStatus() {
            fetch('/api/telegram/unipile/status')
            .then(response => response.json())
            .then(data => {
                const statusDiv = document.getElementById('connectionStatus');
                if (data.success) {
                    const accounts = data.accounts || [];
                    const telegramAccounts = accounts.filter(acc => acc.type === 'TELEGRAM');

                    if (telegramAccounts.length > 0) {
                        let html = '<div class="success"><strong>✅ Connected Telegram Accounts:</strong><br>';
                        telegramAccounts.forEach(account => {
                            html += `📱 Account ID: ${account.id}<br>`;
                            html += `👤 Name: ${account.name || 'N/A'}<br>`;
                            html += `📞 Phone: ${account.phone || 'N/A'}<br>`;
                            html += `🔗 Status: ${account.status || 'Connected'}<br><br>`;
                        });
                        html += '</div>';
                        statusDiv.innerHTML = html;
                    } else {
                        statusDiv.innerHTML = `
                            <div class="info">
                                <strong>📋 Unipile API Connected</strong><br>
                                No Telegram accounts found. To add a Telegram account:<br>
                                1. Visit your <a href="https://dashboard.unipile.com" target="_blank">Unipile Dashboard</a><br>
                                2. Click "Add Account" → "Telegram"<br>
                                3. Follow the authentication process<br>
                                4. Return here and click "Check Connection Status"
                            </div>
                        `;
                    }
                } else {
                    statusDiv.innerHTML = '<div class="error">❌ Failed to get status: ' + data.error + '</div>';
                }
            })
            .catch(error => {
                document.getElementById('connectionStatus').innerHTML =
                    '<div class="error">❌ Error: ' + error.message + '</div>';
            });
        }



        function sendTestMessage() {
            const chatId = document.getElementById('testChatId').value;
            const message = document.getElementById('testMessage').value;
            
            if (!chatId || !message) {
                document.getElementById('testResult').innerHTML = 
                    '<div class="error">❌ Please enter both chat ID and message</div>';
                return;
            }
            
            fetch('/api/telegram/send-test', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    chat_id: chatId,
                    message: message
                })
            })
            .then(response => response.json())
            .then(data => {
                const resultDiv = document.getElementById('testResult');
                if (data.success) {
                    resultDiv.innerHTML = '<div class="success">✅ Test message sent successfully!</div>';
                } else {
                    resultDiv.innerHTML = '<div class="error">❌ Failed to send test message: ' + data.error + '</div>';
                }
            })
            .catch(error => {
                document.getElementById('testResult').innerHTML = 
                    '<div class="error">❌ Error: ' + error.message + '</div>';
            });
        }
        
        function sendBulkMessages() {
            const chatIdsText = document.getElementById('bulkChatIds').value;
            const message = document.getElementById('bulkMessage').value;
            const delay = parseFloat(document.getElementById('bulkDelay').value);

            if (!chatIdsText || !message) {
                document.getElementById('bulkResult').innerHTML =
                    '<div class="error">❌ Please enter both chat IDs and message</div>';
                return;
            }

            // Parse chat IDs (one per line)
            const recipients = chatIdsText.split('\n')
                .map(chatId => chatId.trim())
                .filter(chatId => chatId.length > 0);

            if (recipients.length === 0) {
                document.getElementById('bulkResult').innerHTML =
                    '<div class="error">❌ Please enter at least one chat ID</div>';
                return;
            }

            document.getElementById('bulkResult').innerHTML =
                '<div class="info">📤 Sending bulk messages... Please wait.</div>';

            fetch('/api/telegram/send-bulk', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    recipients: recipients,
                    message: message,
                    delay: delay
                })
            })
            .then(response => response.json())
            .then(data => {
                const resultDiv = document.getElementById('bulkResult');
                if (data.success) {
                    const results = data.results;
                    const successful = results.filter(r => r.result.success || r.result.ok).length;
                    const failed = results.length - successful;

                    let resultHtml = `<div class="success">✅ Bulk messaging completed!</div>`;
                    resultHtml += `<div class="info">📊 Results: ${successful} sent, ${failed} failed</div>`;

                    if (failed > 0) {
                        resultHtml += '<div class="error">❌ Failed messages:<ul>';
                        results.forEach(r => {
                            if (!r.result.success && !r.result.ok) {
                                resultHtml += `<li>${r.chat_id}: ${r.result.error || r.result.description}</li>`;
                            }
                        });
                        resultHtml += '</ul></div>';
                    }

                    resultDiv.innerHTML = resultHtml;
                } else {
                    resultDiv.innerHTML = '<div class="error">❌ Failed to send bulk messages: ' + (data.error || 'Unknown error') + '</div>';
                }
            })
            .catch(error => {
                document.getElementById('bulkResult').innerHTML =
                    '<div class="error">❌ Error: ' + error.message + '</div>';
            });
        }

        function getUpdates() {
            fetch('/api/telegram/updates')
            .then(response => response.json())
            .then(data => {
                const resultDiv = document.getElementById('updatesResult');
                if (data.success && data.data.result) {
                    const updates = data.data.result;
                    const isCached = data.cached || false;
                    const conflictsPrevented = data.conflicts_prevented || 0;

                    if (updates.length === 0) {
                        resultDiv.innerHTML = '<div class="info">📭 No recent messages found. Send a message to your bot to test!</div>';
                    } else {
                        let html = `<div class="success"><strong>📨 Recent Messages ${isCached ? '(Cached)' : '(Fresh)'}:</strong><br>`;
                        if (isCached) {
                            html += `<small style="color: #6c757d;">ℹ️ Showing cached updates to prevent API conflicts. Conflicts prevented: ${conflictsPrevented}</small><br>`;
                        }
                        html += '<br>';
                        updates.slice(-10).forEach(update => {
                            if (update.message) {
                                const msg = update.message;
                                const userName = msg.from.first_name || 'Unknown';
                                const chatId = msg.chat.id;
                                const text = msg.text || '[Media/Other]';
                                const date = new Date(msg.date * 1000).toLocaleString();

                                html += `
                                    <div style="background: #f8f9fa; border: 1px solid #dee2e6; border-radius: 5px; padding: 10px; margin-bottom: 10px;">
                                        <strong>👤 ${userName} (ID: ${chatId})</strong><br>
                                        <small>📅 ${date}</small><br>
                                        <span style="color: #495057;">💬 "${text}"</span>
                                    </div>`;
                            }
                        });
                        html += '</div>';
                        resultDiv.innerHTML = html;
                    }
                } else {
                    resultDiv.innerHTML = '<div class="error">❌ Failed to get updates: ' + (data.error || 'Unknown error') + '</div>';
                }
            })
            .catch(error => {
                document.getElementById('updatesResult').innerHTML =
                    '<div class="error">❌ Error: ' + error.message + '</div>';
            });
        }

        function processMessages() {
            fetch('/api/telegram/process-updates', {
                method: 'POST'
            })
            .then(response => response.json())
            .then(data => {
                const resultDiv = document.getElementById('responseResult');
                if (data.success) {
                    let html = `<div class="success">✅ ${data.message}<br><br>`;

                    if (data.details && data.details.length > 0) {
                        html += '<strong>📋 Bot Responses:</strong><br>';
                        data.details.forEach(detail => {
                            if (detail.user_name) {
                                const status = detail.sent_successfully ? '✅' : '❌';
                                html += `
                                    <div style="background: #f8f9fa; border: 1px solid #dee2e6; border-radius: 5px; padding: 10px; margin-bottom: 10px;">
                                        <strong>👤 ${detail.user_name} (${detail.chat_id})</strong><br>
                                        <span style="color: #6c757d;">📨 User said: "${detail.incoming_message}"</span><br>
                                        <span style="color: #28a745;">🤖 Bot replied: "${detail.response}"</span><br>
                                        <small>${status} ${detail.sent_successfully ? 'Sent successfully' : 'Failed to send'}</small>
                                    </div>`;
                            }
                        });
                    } else {
                        html += '<div class="info">💡 No new messages to process. Send a message to your bot first!</div>';
                    }
                    html += '</div>';
                    resultDiv.innerHTML = html;

                    // Also refresh the updates display
                    getUpdates();
                } else {
                    resultDiv.innerHTML = `<div class="error">❌ Failed to process messages: ${data.detail || 'Unknown error'}</div>`;
                }
            })
            .catch(error => {
                document.getElementById('responseResult').innerHTML =
                    `<div class="error">❌ Error: ${error.message}</div>`;
            });
        }

        function startBotRunner() {
            const resultDiv = document.getElementById('responseResult');
            resultDiv.innerHTML = '<div class="info">🚀 Starting integrated bot polling...</div>';

            fetch('/api/telegram/polling', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    action: 'start',
                    interval: 2.0
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    const botInfo = data.bot_info || {};
                    resultDiv.innerHTML = `
                        <div class="success">
                            <strong>✅ Bot Polling Started Successfully!</strong><br><br>
                            🤖 Bot: @${botInfo.username || 'unknown'}<br>
                            🆔 ID: ${botInfo.id || 'unknown'}<br>
                            ⏱️ Interval: ${data.interval} seconds<br><br>

                            <strong>Your bot is now automatically responding to messages!</strong><br>
                            • Send messages to your bot on Telegram<br>
                            • The bot will respond automatically<br>
                            • No need to run separate scripts<br><br>

                            <button onclick="stopBotRunner()" style="background-color: #dc3545;">🛑 Stop Bot Polling</button>
                            <button onclick="checkBotStatus()" style="background-color: #17a2b8;">📊 Check Status</button>
                        </div>
                    `;
                } else {
                    resultDiv.innerHTML = `
                        <div class="error">
                            ❌ Failed to start bot polling: ${data.detail || data.error || 'Unknown error'}<br><br>
                            💡 Make sure your bot token is configured correctly.
                        </div>
                    `;
                }
            })
            .catch(error => {
                resultDiv.innerHTML = `
                    <div class="error">
                        ❌ Error starting bot polling: ${error.message}<br><br>
                        💡 Make sure your API server is running.
                    </div>
                `;
            });
        }

        function stopBotRunner() {
            const resultDiv = document.getElementById('responseResult');
            resultDiv.innerHTML = '<div class="info">🛑 Stopping bot polling...</div>';

            fetch('/api/telegram/polling', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    action: 'stop'
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    const stats = data.stats || {};
                    resultDiv.innerHTML = `
                        <div class="success">
                            <strong>🛑 Bot Polling Stopped Successfully!</strong><br><br>
                            📊 Session Statistics:<br>
                            • Messages processed: ${stats.messages_processed || 0}<br>
                            • Responses sent: ${stats.responses_sent || 0}<br>
                            • Errors: ${stats.errors || 0}<br><br>

                            <button onclick="startBotRunner()" style="background-color: #28a745;">🚀 Start Bot Polling</button>
                        </div>
                    `;
                } else {
                    resultDiv.innerHTML = `<div class="error">❌ Failed to stop bot polling: ${data.detail || 'Unknown error'}</div>`;
                }
            })
            .catch(error => {
                resultDiv.innerHTML = `<div class="error">❌ Error stopping bot polling: ${error.message}</div>`;
            });
        }

        function checkBotStatus() {
            fetch('/api/telegram/polling/status')
            .then(response => response.json())
            .then(data => {
                const resultDiv = document.getElementById('responseResult');
                if (data.success) {
                    const polling = data.polling;
                    const stats = polling.stats || {};

                    resultDiv.innerHTML = `
                        <div class="info">
                            <strong>📊 Bot Polling Status</strong><br><br>
                            🔄 Status: ${polling.is_running ? '✅ Running' : '❌ Stopped'}<br>
                            🤖 Bot Configured: ${polling.bot_configured ? '✅ Yes' : '❌ No'}<br><br>

                            📈 Statistics:<br>
                            • Messages processed: ${stats.messages_processed || 0}<br>
                            • Responses sent: ${stats.responses_sent || 0}<br>
                            • Errors: ${stats.errors || 0}<br>
                            • Start time: ${stats.start_time ? new Date(stats.start_time).toLocaleString() : 'Not started'}<br>
                            • Last activity: ${stats.last_activity ? new Date(stats.last_activity).toLocaleString() : 'None'}<br><br>

                            ${polling.is_running ?
                                '<button onclick="stopBotRunner()" style="background-color: #dc3545;">🛑 Stop Polling</button>' :
                                '<button onclick="startBotRunner()" style="background-color: #28a745;">🚀 Start Polling</button>'
                            }
                        </div>
                    `;
                } else {
                    resultDiv.innerHTML = `<div class="error">❌ Failed to get status: ${data.error || 'Unknown error'}</div>`;
                }
            })
            .catch(error => {
                document.getElementById('responseResult').innerHTML = `<div class="error">❌ Error: ${error.message}</div>`;
            });
        }

        function openBotTester() {
            // Open the bot tester in a new window
            window.open('/telegram/bot_tester.html', '_blank');
        }
    </script>
</body>
</html>
