"""
Enhanced Telegram Messaging Demo
Demonstrates all the improved features and capabilities
"""

import asyncio
import time
from datetime import datetime
from telegram_api import TelegramMessaging, MessageResult, ConnectionStatus, sanitize_message, format_html_message

def print_separator(title: str):
    """Print a nice separator for demo sections"""
    print("\n" + "="*60)
    print(f"🚀 {title}")
    print("="*60)

def print_result(result, operation: str):
    """Print operation result in a nice format"""
    if isinstance(result, MessageResult):
        status = "✅ SUCCESS" if result.success else "❌ FAILED"
        print(f"{status} - {operation}")
        if result.success:
            print(f"   📧 Message ID: {result.message_id}")
            print(f"   🔧 Method: {result.method}")
            if result.retry_count > 0:
                print(f"   🔄 Retries: {result.retry_count}")
        else:
            print(f"   ❌ Error: {result.error}")
        if result.timestamp:
            print(f"   ⏰ Time: {result.timestamp.strftime('%H:%M:%S')}")
    elif isinstance(result, dict):
        status = "✅ SUCCESS" if result.get("success", False) else "❌ FAILED"
        print(f"{status} - {operation}")
        if result.get("error"):
            print(f"   ❌ Error: {result['error']}")
        if result.get("message"):
            print(f"   💬 Message: {result['message']}")
    else:
        print(f"📋 {operation}: {result}")

def demo_message_validation():
    """Demo message validation and sanitization"""
    print_separator("Message Validation & Sanitization")
    
    test_messages = [
        "Hello, world! 👋",
        "Message with\x00control\x08characters\x1F",
        "",
        None,
        "A" * 5000,  # Very long message
        "<script>alert('xss')</script>Normal text",
        "Message with & special < characters >"
    ]
    
    for i, msg in enumerate(test_messages, 1):
        print(f"\n{i}. Testing message: {repr(msg)[:50]}...")
        try:
            sanitized = sanitize_message(msg, max_length=100)
            print(f"   ✅ Sanitized: {repr(sanitized)[:50]}...")
            
            if sanitized:
                formatted = format_html_message(sanitized, bold=True)
                print(f"   🎨 HTML formatted: {formatted[:50]}...")
        except Exception as e:
            print(f"   ❌ Error: {e}")

def demo_connection_management():
    """Demo connection management and health monitoring"""
    print_separator("Connection Management & Health Monitoring")
    
    # Test with invalid token first
    print("\n1. Testing with invalid token...")
    telegram_invalid = TelegramMessaging(bot_token="invalid_token")
    
    connection_test = telegram_invalid.test_connection()
    print_result(connection_test, "Connection test (invalid token)")
    
    health = telegram_invalid.get_connection_health()
    print(f"\n📊 Connection Health:")
    print(f"   Status: {health['status']}")
    print(f"   Bot configured: {health['bot_configured']}")
    print(f"   Unipile configured: {health['unipile_configured']}")
    print(f"   Rate limit: {health['rate_limit_config']['messages_per_second']}/sec")
    
    # Test with configured token (from config)
    print("\n2. Testing with configured token...")
    telegram = TelegramMessaging()
    
    connection_test = telegram.test_connection()
    print_result(connection_test, "Connection test (configured token)")
    
    if connection_test.get("success"):
        print(f"   🤖 Bot: @{connection_test['bot_info']['username']}")
        print(f"   🆔 ID: {connection_test['bot_info']['id']}")
        print(f"   ⏱️ Test duration: {connection_test['test_duration_ms']}ms")

def demo_rate_limiting():
    """Demo rate limiting functionality"""
    print_separator("Rate Limiting Demo")
    
    telegram = TelegramMessaging()
    
    print("Testing rate limiting with multiple rapid calls...")
    
    start_time = time.time()
    for i in range(3):
        print(f"\n{i+1}. Making rate-limited call...")
        call_start = time.time()
        telegram._enhanced_rate_limit(f"test_chat_{i}")
        call_duration = time.time() - call_start
        print(f"   ⏱️ Call took: {call_duration:.3f} seconds")
    
    total_time = time.time() - start_time
    print(f"\n📊 Total time for 3 calls: {total_time:.3f} seconds")
    print("   (Should show increasing delays due to rate limiting)")

def demo_message_sending():
    """Demo enhanced message sending"""
    print_separator("Enhanced Message Sending")
    
    telegram = TelegramMessaging()
    
    # Test message validation
    print("\n1. Testing message validation...")
    
    test_cases = [
        ("", "Empty message"),
        (None, "None message"),
        ("Valid message", "Valid message"),
        ("A" * 5000, "Very long message (will be truncated)")
    ]
    
    for message, description in test_cases:
        print(f"\n   Testing: {description}")
        # Use a test chat ID (won't actually send due to invalid token, but will test validation)
        result = telegram.send_message("123456789", message)
        print_result(result, f"Send message ({description})")

def demo_bulk_messaging():
    """Demo bulk messaging capabilities"""
    print_separator("Bulk Messaging Demo")
    
    telegram = TelegramMessaging()
    
    recipients = ["123456789", "987654321", "555666777"]
    message = "This is a test bulk message! 📢"
    
    print(f"Sending bulk message to {len(recipients)} recipients...")
    print(f"Message: {message}")
    
    start_time = time.time()
    results = telegram.send_bulk_messages(recipients, message, delay=0.1)
    duration = time.time() - start_time
    
    print(f"\n📊 Bulk messaging completed in {duration:.2f} seconds")
    print(f"   Total recipients: {len(recipients)}")
    print(f"   Results returned: {len(results)}")
    
    # Show summary of results
    successful = sum(1 for r in results if r.get("result", {}).get("success", False))
    failed = len(results) - successful
    
    print(f"   ✅ Successful: {successful}")
    print(f"   ❌ Failed: {failed}")

async def demo_async_messaging():
    """Demo async messaging capabilities"""
    print_separator("Async Messaging Demo")
    
    telegram = TelegramMessaging()
    
    print("Testing async message sending...")
    
    start_time = time.time()
    result = await telegram.send_message_async("123456789", "Async test message! ⚡")
    duration = time.time() - start_time
    
    print_result(result, f"Async message send ({duration:.3f}s)")
    
    # Demo async bulk messaging
    print("\nTesting async bulk messaging...")
    recipients = ["123", "456", "789"]
    
    start_time = time.time()
    results = await telegram.send_bulk_messages_async(recipients, "Async bulk message! 🚀")
    duration = time.time() - start_time
    
    print(f"\n📊 Async bulk messaging completed in {duration:.2f} seconds")
    print(f"   Recipients: {len(recipients)}")
    print(f"   Results: {len(results)}")

def main():
    """Run all demos"""
    print("🤖 Enhanced Telegram Messaging Integration Demo")
    print("=" * 60)
    print("This demo showcases all the enhanced features and improvements")
    
    try:
        # Run synchronous demos
        demo_message_validation()
        demo_connection_management()
        demo_rate_limiting()
        demo_message_sending()
        demo_bulk_messaging()
        
        # Run async demo
        print_separator("Running Async Demo")
        asyncio.run(demo_async_messaging())
        
        print_separator("Demo Complete! 🎉")
        print("✅ All enhanced features demonstrated successfully!")
        print("\n🔧 Key improvements showcased:")
        print("   • Enhanced error handling and retry logic")
        print("   • Message validation and sanitization")
        print("   • Advanced rate limiting")
        print("   • Connection health monitoring")
        print("   • Async messaging support")
        print("   • Improved bulk messaging")
        print("   • Rich message formatting")
        print("   • Comprehensive logging")
        
    except Exception as e:
        print(f"\n❌ Demo error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
