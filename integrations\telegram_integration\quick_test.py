"""
Quick test to verify the enhanced Telegram integration works
"""

import sys
import os

# Add current directory to path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_imports():
    """Test that all imports work correctly"""
    try:
        from telegram_api import (
            TelegramMessaging, UnipileClient, MessageResult, ConnectionStatus,
            sanitize_message, format_html_message, RateLimitConfig
        )
        print("✅ All imports successful")
        return True
    except Exception as e:
        print(f"❌ Import failed: {e}")
        return False

def test_utility_functions():
    """Test utility functions"""
    try:
        from telegram_api import sanitize_message, format_html_message
        
        # Test sanitization
        result = sanitize_message("Hello, world!")
        assert result == "Hello, world!", f"Expected 'Hello, world!', got '{result}'"
        
        # Test HTML formatting
        result = format_html_message("Test", bold=True)
        assert result == "<b>Test</b>", f"Expected '<b>Test</b>', got '{result}'"
        
        print("✅ Utility functions work correctly")
        return True
    except Exception as e:
        print(f"❌ Utility function test failed: {e}")
        return False

def test_basic_initialization():
    """Test basic initialization without config file dependencies"""
    try:
        from telegram_api import TelegramMessaging
        
        # Test with explicit parameters to avoid config file issues
        telegram = TelegramMessaging(
            bot_token="test_token",
            unipile_api_key="test_key"
        )
        
        print("✅ Basic initialization successful")
        print(f"   Bot token set: {bool(telegram.bot_token)}")
        print(f"   Connection status: {telegram.connection_status}")
        print(f"   Rate limit config: {telegram.rate_limit.messages_per_second}/sec")
        return True
    except Exception as e:
        print(f"❌ Basic initialization failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_message_result():
    """Test MessageResult class"""
    try:
        from telegram_api import MessageResult
        from datetime import datetime
        
        result = MessageResult(
            success=True,
            message_id="123",
            platform="telegram",
            method="test",
            timestamp=datetime.now()
        )
        
        print("✅ MessageResult creation successful")
        print(f"   Success: {result.success}")
        print(f"   Message ID: {result.message_id}")
        print(f"   Platform: {result.platform}")
        return True
    except Exception as e:
        print(f"❌ MessageResult test failed: {e}")
        return False

def test_connection_status():
    """Test ConnectionStatus enum"""
    try:
        from telegram_api import ConnectionStatus
        
        status = ConnectionStatus.CONNECTED
        print("✅ ConnectionStatus enum works")
        print(f"   Connected value: {status.value}")
        print(f"   All statuses: {[s.value for s in ConnectionStatus]}")
        return True
    except Exception as e:
        print(f"❌ ConnectionStatus test failed: {e}")
        return False

def main():
    """Run all quick tests"""
    print("🚀 Quick Test Suite for Enhanced Telegram Integration")
    print("=" * 60)
    
    tests = [
        ("Import Test", test_imports),
        ("Utility Functions", test_utility_functions),
        ("Basic Initialization", test_basic_initialization),
        ("MessageResult Class", test_message_result),
        ("ConnectionStatus Enum", test_connection_status)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n🧪 Running: {test_name}")
        print("-" * 40)
        if test_func():
            passed += 1
        else:
            print(f"❌ {test_name} failed")
    
    print("\n" + "=" * 60)
    print(f"📊 Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! The enhanced Telegram integration is working correctly.")
        print("\n🔧 Key features verified:")
        print("   • Enhanced imports and class structure")
        print("   • Message validation and sanitization")
        print("   • HTML formatting capabilities")
        print("   • Improved initialization process")
        print("   • Better error handling structures")
    else:
        print(f"⚠️  {total - passed} tests failed. Please check the issues above.")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
