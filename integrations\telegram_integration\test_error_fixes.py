"""
Test script to verify the error fixes for:
1. MessageResult object attribute errors
2. <PERSON><PERSON> not found error handling
"""

import requests
import json

def test_valid_chat_id():
    """Test with a valid chat ID that should work"""
    print("🧪 Testing with Valid Chat ID")
    print("=" * 30)
    
    base_url = "http://localhost:8000"
    
    # Use your actual chat ID
    valid_chat_id = "5001883382"  # Replace with your chat ID
    
    try:
        response = requests.post(
            f"{base_url}/api/telegram/send-test",
            json={
                "chat_id": valid_chat_id,
                "message": "🧪 Test message - checking error fixes!"
            },
            headers={"Content-Type": "application/json"}
        )
        
        print(f"Status Code: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print("✅ API call successful!")
            print(f"Success: {data.get('success')}")
            
            if data.get('success'):
                result = data.get('result', {})
                print(f"📨 Message ID: {result.get('message_id')}")
                print(f"🔧 Method: {result.get('method')}")
                print(f"📱 Platform: {result.get('platform')}")
                print("✅ MessageResult object handling is working!")
            else:
                print(f"❌ Message failed: {data.get('error')}")
        else:
            print(f"❌ HTTP Error: {response.status_code}")
            try:
                error_data = response.json()
                print(f"Error: {error_data.get('detail', 'Unknown error')}")
            except:
                print(f"Response: {response.text}")
                
    except Exception as e:
        print(f"❌ Test error: {e}")
        return False
    
    return True

def test_invalid_username():
    """Test with an invalid username to check error handling"""
    print("\n🧪 Testing with Invalid Username")
    print("=" * 30)
    
    base_url = "http://localhost:8000"
    
    # Use a username that doesn't exist
    invalid_username = "nonexistentuser12345"
    
    try:
        response = requests.post(
            f"{base_url}/api/telegram/send-test",
            json={
                "chat_id": invalid_username,
                "message": "This should fail with chat not found"
            },
            headers={"Content-Type": "application/json"}
        )
        
        print(f"Status Code: {response.status_code}")
        
        if response.status_code == 400:
            data = response.json()
            error_msg = data.get('detail', 'Unknown error')
            print("✅ Got expected 400 error")
            print(f"Error message: {error_msg}")
            
            if "chat not found" in error_msg.lower():
                print("✅ Chat not found error detected correctly")
            else:
                print("⚠️  Different error than expected")
                
        elif response.status_code == 200:
            print("⚠️  Unexpected success - username might actually exist")
            
        else:
            print(f"❌ Unexpected status code: {response.status_code}")
            
    except Exception as e:
        print(f"❌ Test error: {e}")
        return False
    
    return True

def test_invalid_format():
    """Test with invalid chat identifier format"""
    print("\n🧪 Testing with Invalid Format")
    print("=" * 30)
    
    base_url = "http://localhost:8000"
    
    # Use an invalid format
    invalid_format = "ab"  # Too short for username
    
    try:
        response = requests.post(
            f"{base_url}/api/telegram/send-test",
            json={
                "chat_id": invalid_format,
                "message": "This should fail with validation error"
            },
            headers={"Content-Type": "application/json"}
        )
        
        print(f"Status Code: {response.status_code}")
        
        if response.status_code == 400:
            data = response.json()
            error_msg = data.get('detail', 'Unknown error')
            print("✅ Got expected 400 error")
            print(f"Error message: {error_msg}")
            
            if "Invalid chat identifier" in error_msg:
                print("✅ Validation error detected correctly")
            else:
                print("⚠️  Different validation error than expected")
                
        else:
            print(f"❌ Unexpected status code: {response.status_code}")
            
    except Exception as e:
        print(f"❌ Test error: {e}")
        return False
    
    return True

def test_bulk_messaging():
    """Test bulk messaging to ensure MessageResult handling works"""
    print("\n🧪 Testing Bulk Messaging")
    print("=" * 30)
    
    base_url = "http://localhost:8000"
    
    # Mix of valid and invalid recipients
    recipients = [
        "5001883382",  # Valid chat ID
        "nonexistentuser",  # Invalid username
        "@fakechannel"  # Invalid channel
    ]
    
    try:
        response = requests.post(
            f"{base_url}/api/telegram/send-bulk",
            json={
                "recipients": recipients,
                "message": "🧪 Bulk test - checking error handling",
                "delay": 1.0
            },
            headers={"Content-Type": "application/json"}
        )
        
        print(f"Status Code: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print("✅ Bulk API call successful!")
            
            summary = data.get('summary', {})
            print(f"📊 Summary:")
            print(f"   Total: {summary.get('total', 0)}")
            print(f"   Successful: {summary.get('successful', 0)}")
            print(f"   Failed: {summary.get('failed', 0)}")
            
            # Check individual results
            results = data.get('results', [])
            print(f"\n📋 Individual Results:")
            for result in results:
                chat_id = result.get('chat_id')
                result_data = result.get('result', {})
                success = result_data.get('success', False)
                error = result_data.get('error', '')
                
                status = "✅" if success else "❌"
                print(f"   {status} {chat_id}: {error if error else 'Success'}")
            
            print("✅ Bulk MessageResult handling is working!")
            
        else:
            print(f"❌ HTTP Error: {response.status_code}")
            
    except Exception as e:
        print(f"❌ Bulk test error: {e}")
        return False
    
    return True

def main():
    """Run all error fix tests"""
    print("🔧 Error Fixes Test Suite")
    print("=" * 50)
    
    print("🎯 Testing fixes for:")
    print("1. MessageResult object attribute errors")
    print("2. Chat not found error handling")
    print("3. Better error messages and guidance")
    
    # Test 1: Valid chat ID (should work)
    success1 = test_valid_chat_id()
    
    # Test 2: Invalid username (should fail gracefully)
    success2 = test_invalid_username()
    
    # Test 3: Invalid format (should validate)
    success3 = test_invalid_format()
    
    # Test 4: Bulk messaging (should handle mixed results)
    success4 = test_bulk_messaging()
    
    print("\n" + "=" * 50)
    if all([success1, success2, success3, success4]):
        print("🎊 All error fixes are working!")
        print("\n✅ What was fixed:")
        print("• MessageResult objects handled correctly in API responses")
        print("• Better error messages for chat not found")
        print("• Helpful guidance for different error types")
        print("• Robust bulk messaging error handling")
        print("• Improved web interface feedback")
        
        print("\n🚀 Your messaging system is now more robust!")
        print("• Clear error messages help users understand issues")
        print("• Proper handling of both success and failure cases")
        print("• Better user experience in the web interface")
    else:
        print("⚠️  Some tests failed. Check the errors above.")
        
    print("\n💡 Tips for using usernames:")
    print("• Users must start a conversation with your bot first")
    print("• Use numeric chat IDs for private users without usernames")
    print("• Check recent messages to find valid chat IDs")
    print("• For groups/channels, add your bot as a member first")

if __name__ == "__main__":
    main()
