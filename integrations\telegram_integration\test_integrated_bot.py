"""
Test script for the integrated bot polling system
Tests the new API endpoints for starting/stopping bot polling
"""

import requests
import time
import json

def test_integrated_bot_polling():
    """Test the integrated bot polling system"""
    print("🧪 Testing Integrated Bot Polling System")
    print("=" * 50)
    
    base_url = "http://localhost:8000"
    
    try:
        # Test 1: Check current polling status
        print("\n1. 📊 Checking current polling status...")
        response = requests.get(f"{base_url}/api/telegram/polling/status")
        if response.status_code == 200:
            data = response.json()
            polling = data.get("polling", {})
            print(f"   Status: {'✅ Running' if polling.get('is_running') else '❌ Stopped'}")
            print(f"   Bot configured: {'✅ Yes' if polling.get('bot_configured') else '❌ No'}")
            
            stats = polling.get("stats", {})
            print(f"   Messages processed: {stats.get('messages_processed', 0)}")
            print(f"   Responses sent: {stats.get('responses_sent', 0)}")
        else:
            print(f"   ❌ Failed to get status: {response.status_code}")
        
        # Test 2: Start bot polling
        print("\n2. 🚀 Starting bot polling...")
        start_data = {
            "action": "start",
            "interval": 2.0
        }
        response = requests.post(
            f"{base_url}/api/telegram/polling",
            json=start_data,
            headers={"Content-Type": "application/json"}
        )
        
        if response.status_code == 200:
            data = response.json()
            if data.get("success"):
                print("   ✅ Bot polling started successfully!")
                bot_info = data.get("bot_info", {})
                print(f"   🤖 Bot: @{bot_info.get('username', 'unknown')}")
                print(f"   🆔 ID: {bot_info.get('id', 'unknown')}")
                print(f"   ⏱️ Interval: {data.get('interval')} seconds")
            else:
                print(f"   ❌ Failed to start: {data.get('error')}")
                return False
        else:
            print(f"   ❌ HTTP Error: {response.status_code}")
            try:
                error_data = response.json()
                print(f"   Error: {error_data.get('detail', 'Unknown error')}")
            except:
                print(f"   Response: {response.text}")
            return False
        
        # Test 3: Wait and check status again
        print("\n3. ⏳ Waiting 5 seconds to test polling...")
        time.sleep(5)
        
        response = requests.get(f"{base_url}/api/telegram/polling/status")
        if response.status_code == 200:
            data = response.json()
            polling = data.get("polling", {})
            print(f"   Status: {'✅ Running' if polling.get('is_running') else '❌ Stopped'}")
            
            stats = polling.get("stats", {})
            print(f"   Messages processed: {stats.get('messages_processed', 0)}")
            print(f"   Responses sent: {stats.get('responses_sent', 0)}")
            print(f"   Errors: {stats.get('errors', 0)}")
        
        # Test 4: Test manual message processing (should still work)
        print("\n4. 📨 Testing manual message processing...")
        response = requests.post(f"{base_url}/api/telegram/process-updates")
        if response.status_code == 200:
            data = response.json()
            print(f"   ✅ Manual processing works: {data.get('message')}")
        elif response.status_code == 404:
            print("   ⚠️  Manual processing endpoint not found (may need server restart)")
        else:
            print(f"   ❌ Manual processing failed: {response.status_code}")
        
        # Test 5: Stop bot polling
        print("\n5. 🛑 Stopping bot polling...")
        stop_data = {
            "action": "stop"
        }
        response = requests.post(
            f"{base_url}/api/telegram/polling",
            json=stop_data,
            headers={"Content-Type": "application/json"}
        )
        
        if response.status_code == 200:
            data = response.json()
            if data.get("success"):
                print("   ✅ Bot polling stopped successfully!")
                stats = data.get("stats", {})
                print(f"   📊 Final stats:")
                print(f"      Messages processed: {stats.get('messages_processed', 0)}")
                print(f"      Responses sent: {stats.get('responses_sent', 0)}")
                print(f"      Errors: {stats.get('errors', 0)}")
            else:
                print(f"   ❌ Failed to stop: {data.get('error')}")
        else:
            print(f"   ❌ HTTP Error: {response.status_code}")
        
        print("\n" + "=" * 50)
        print("🎉 Integrated Bot Polling Test Complete!")
        print("\n💡 How to use:")
        print("1. Your API server now includes bot polling")
        print("2. Use the web interface to start/stop polling")
        print("3. Bot will automatically respond to messages")
        print("4. No need to run separate bot_runner.py")
        print("5. Everything runs in one server!")
        
        return True
        
    except requests.exceptions.ConnectionError:
        print("❌ Cannot connect to API server")
        print("💡 Make sure your API server is running:")
        print("   cd integrations/api")
        print("   python api_endpoints.py")
        return False
        
    except Exception as e:
        print(f"❌ Test error: {e}")
        return False

def test_bot_endpoints():
    """Test other bot-related endpoints"""
    print("\n🧪 Testing Other Bot Endpoints")
    print("-" * 30)
    
    base_url = "http://localhost:8000"
    
    endpoints = [
        ("GET", "/api/telegram/test", "Bot connection test"),
        ("GET", "/api/telegram/bot-info", "Bot information"),
        ("GET", "/api/telegram/updates", "Get updates")
    ]
    
    for method, endpoint, description in endpoints:
        try:
            print(f"\n📡 Testing {description}: {method} {endpoint}")
            
            if method == "GET":
                response = requests.get(f"{base_url}{endpoint}")
            else:
                response = requests.post(f"{base_url}{endpoint}")
            
            print(f"   Status: {response.status_code}")
            
            if response.status_code == 200:
                data = response.json()
                if data.get("success"):
                    print("   ✅ Endpoint working")
                else:
                    print(f"   ⚠️  Endpoint returned error: {data.get('error', 'Unknown')}")
            else:
                print(f"   ❌ HTTP Error: {response.status_code}")
                
        except Exception as e:
            print(f"   ❌ Error: {e}")

def main():
    """Run all tests"""
    print("🤖 Integrated Telegram Bot Testing Suite")
    print("=" * 60)
    
    # Test integrated polling
    success = test_integrated_bot_polling()
    
    # Test other endpoints
    test_bot_endpoints()
    
    print("\n" + "=" * 60)
    if success:
        print("🎊 All tests completed! Your integrated bot system is ready!")
        print("\n🚀 Next steps:")
        print("1. Open: http://localhost:8000/telegram/telegram_auth.html")
        print("2. Click 'Start Bot Runner' to enable automatic responses")
        print("3. Send messages to your bot - it will respond automatically!")
        print("4. Monitor status and statistics in the web interface")
    else:
        print("⚠️  Some tests failed. Check the errors above.")

if __name__ == "__main__":
    main()
